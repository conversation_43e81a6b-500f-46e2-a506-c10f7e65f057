{"components": [{"key": "header_preop_panel", "html": "</br><h4>Pre-Operative Labwork Intake</h4><p>Please complete this short intake so we can arrange the correct pre-op tests required by your surgeon/hospital.</p>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "preop_indication", "type": "selectboxes", "input": true, "label": "What is the reason for your pre-operative testing?", "values": [{"label": "Requested by surgeon/hospital", "value": "surgeon_requested"}, {"label": "Dentist request", "value": "dental_oral"}, {"label": "Medication monitoring before surgery (e.g., blood thinners)", "value": "med_monitoring"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Phase that summarizes the question:"}, {"key": "preop_indication_other_text", "type": "textfield", "input": true, "label": "Please specify the reason:", "validate": {"required": true, "minLength": 3}, "tableView": true, "placeholder": "Describe the reason for testing", "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.preop_indication && data.preop_indication.other;"}, {"key": "procedure_info_header", "html": "</br><h4>Procedure Details</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.preop_indication && Object.values(data.preop_indication).some(<PERSON><PERSON><PERSON>);"}, {"key": "procedure_body_region", "type": "selectboxes", "input": true, "label": "What part of your body will be operated on? (Check all that apply)", "values": [{"label": "Head & Neck (eyes, hair, skin, nose, ears, throat, teeth)", "value": "head_neck"}, {"label": "Chest & Belly (breast, chest, stomach, heart, lungs)", "value": "torso"}, {"label": "Pelvis & Private Parts (women's health, bladder/kidney)", "value": "pelvis_gu"}, {"label": "Bones & Joints (arms, legs, back, spine)", "value": "musculoskeletal"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Surgery location:", "customConditional": "show = data.preop_indication && Object.values(data.preop_indication).some(<PERSON><PERSON><PERSON>);"}, {"key": "procedure_body_area_head_neck", "type": "selectboxes", "input": true, "label": "Which part of your head or neck? (Check all that apply)", "values": [{"label": "Eyes or eyelids", "value": "eye"}, {"label": "Hair or scalp", "value": "hair"}, {"label": "Skin", "value": "skin"}, {"label": "Nose or sinuses", "value": "nose"}, {"label": "Ears or throat", "value": "ent"}, {"label": "Teeth or mouth", "value": "dental"}], "validate": {"custom": "valid = !data.procedure_body_region?.head_neck || Object.values(data.procedure_body_area_head_neck || {}).some(<PERSON><PERSON><PERSON>);", "required": true}, "tableView": true, "confirm_label": "Head/neck surgery:", "customConditional": "show = data.procedure_body_region?.head_neck && data.procedure_body_region && Object.values(data.procedure_body_region).some(<PERSON><PERSON><PERSON>);"}, {"key": "procedure_body_area_torso", "type": "selectboxes", "input": true, "label": "Which part of your chest or belly? (Check all that apply)", "values": [{"label": "Breast or chest", "value": "breast"}, {"label": "Belly or stomach", "value": "abdomen"}, {"label": "Heart", "value": "cardiac"}, {"label": "<PERSON><PERSON><PERSON>", "value": "thoracic"}], "validate": {"custom": "valid = !data.procedure_body_region?.torso || Object.values(data.procedure_body_area_torso || {}).some(<PERSON><PERSON><PERSON>);", "required": true}, "tableView": true, "confirm_label": "Chest/belly surgery:", "customConditional": "show = data.procedure_body_region?.torso && data.procedure_body_region && Object.values(data.procedure_body_region).some(<PERSON><PERSON><PERSON>);"}, {"key": "procedure_body_area_pelvis_gu", "type": "selectboxes", "input": true, "label": "Which area? (Check all that apply)", "values": [{"label": "Women's health (uterus, ovaries, etc.)", "value": "gyne"}, {"label": "Bladder, kidney, or private parts", "value": "urology"}], "validate": {"custom": "valid = !data.procedure_body_region?.pelvis_gu || Object.values(data.procedure_body_area_pelvis_gu || {}).some(<PERSON><PERSON><PERSON>);", "required": true}, "tableView": true, "confirm_label": "Pelvis/private parts surgery:", "customConditional": "show = data.procedure_body_region?.pelvis_gu && data.procedure_body_region && Object.values(data.procedure_body_region).some(<PERSON><PERSON><PERSON>);"}, {"key": "procedure_body_area_musculoskeletal", "type": "selectboxes", "input": true, "label": "Which bones or joints? (Check all that apply)", "values": [{"label": "Arms, legs, or other bones/joints", "value": "ortho"}, {"label": "Back or spine", "value": "spine"}], "validate": {"custom": "valid = !data.procedure_body_region?.musculoskeletal || Object.values(data.procedure_body_area_musculoskeletal || {}).some(<PERSON><PERSON><PERSON>);", "required": true}, "tableView": true, "confirm_label": "Bone/joint surgery:", "customConditional": "show = data.procedure_body_region?.musculoskeletal && data.procedure_body_region && Object.values(data.procedure_body_region).some(<PERSON><PERSON><PERSON>);"}, {"key": "procedure_body_area_other_text", "type": "textfield", "input": true, "label": "Please specify the body area:", "validate": {"required": true, "minLength": 2}, "tableView": true, "placeholder": "Describe the area", "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_region?.other && data.procedure_body_region && Object.values(data.procedure_body_region).some(<PERSON><PERSON><PERSON>);"}, {"key": "procedure_category", "type": "radio", "input": true, "label": "Is this surgery mainly for medical reasons or cosmetic reasons?", "values": [{"label": "Medical reasons (to fix a health problem)", "value": "medical"}, {"label": "Cosmetic reasons (to improve appearance)", "value": "cosmetic"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Surgery type:", "customConditional": "show = (data.procedure_body_region && Object.values(data.procedure_body_region).some(<PERSON><PERSON><PERSON>)) && ((data.procedure_body_region.head_neck && data.procedure_body_area_head_neck && Object.values(data.procedure_body_area_head_neck).some(<PERSON><PERSON><PERSON>)) || (data.procedure_body_region.torso && data.procedure_body_area_torso && Object.values(data.procedure_body_area_torso).some(<PERSON><PERSON><PERSON>)) || (data.procedure_body_region.pelvis_gu && data.procedure_body_area_pelvis_gu && Object.values(data.procedure_body_area_pelvis_gu).some(<PERSON><PERSON><PERSON>)) || (data.procedure_body_region.musculoskeletal && data.procedure_body_area_musculoskeletal && Object.values(data.procedure_body_area_musculoskeletal).some(<PERSON><PERSON><PERSON>)) || data.procedure_body_area_other_text);"}, {"key": "surgery_location_country", "type": "radio", "input": true, "label": "Where will your surgery be done?", "values": [{"label": "Inside Canada", "value": "inside_canada"}, {"label": "Outside Canada", "value": "outside_canada"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Surgery location:", "customConditional": "show = !!data.procedure_category;"}, {"key": "eye_header", "html": "</br><h4>Eye/Eyelids</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.procedure_body_area_head_neck?.eye;"}, {"key": "eye_procedure_known", "type": "radio", "input": true, "label": "Can you name the eye procedure?", "values": [{"label": "Yes - I can specify", "value": "specify"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_head_neck?.eye;"}, {"key": "eye_procedure_text", "type": "textfield", "input": true, "label": "What is the name of the eye procedure?", "html": "</br><h4>Eye/Eyelids</h4>", "validate": {"required": true, "minLength": 3}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_head_neck?.eye && data.eye_procedure_known === 'specify';"}, {"key": "hair_header", "html": "</br><h4>Hair/Scalp</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.procedure_body_area_head_neck?.hair;"}, {"key": "hair_procedure_known", "type": "radio", "input": true, "label": "Can you name the hair/scalp procedure?", "values": [{"label": "Yes - I can specify", "value": "specify"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_head_neck?.hair;"}, {"key": "hair_procedure_text", "type": "textfield", "input": true, "label": "What is the name of the hair/scalp procedure?", "placeholder": "e.g., hair transplant (FUE/FUT), lesion excision", "validate": {"required": true, "minLength": 3}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_head_neck?.hair && data.hair_procedure_known === 'specify';"}, {"key": "skin_header", "html": "</br><h4>Skin/Soft Tissue</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.procedure_body_area_head_neck?.skin;"}, {"key": "skin_procedure_known", "type": "radio", "input": true, "label": "Can you name the skin procedure?", "values": [{"label": "Yes - I can specify", "value": "specify"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_head_neck?.skin;"}, {"key": "skin_procedure_text", "type": "textfield", "input": true, "label": "What is the name of the skin procedure?", "placeholder": "e.g., Mohs surgery, wide local excision, scar revision", "validate": {"required": true, "minLength": 3}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_head_neck?.skin && data.skin_procedure_known === 'specify';"}, {"key": "nose_header", "html": "</br><h4>Nose/Sinuses</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.procedure_body_area_head_neck?.nose;"}, {"key": "nose_procedure_known", "type": "radio", "input": true, "label": "Can you name the nose/sinus procedure?", "values": [{"label": "Yes - I can specify", "value": "specify"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_head_neck?.nose;"}, {"key": "nose_procedure_text", "type": "textfield", "input": true, "label": "What is the name of the nose/sinus procedure?", "placeholder": "e.g., septoplasty, FESS, turbinate reduction, rhinoplasty", "validate": {"required": true, "minLength": 3}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_head_neck?.nose && data.nose_procedure_known === 'specify';"}, {"key": "ent_header", "html": "</br><h4>Ear/Throat (ENT)</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.procedure_body_area_head_neck?.ent;"}, {"key": "ent_procedure_known", "type": "radio", "input": true, "label": "Can you name the ENT procedure?", "values": [{"label": "Yes - I can specify", "value": "specify"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_head_neck?.ent;"}, {"key": "ent_procedure_text", "type": "textfield", "input": true, "label": "What is the name of the ENT procedure?", "placeholder": "e.g., tonsillectomy, adenoidectomy, tympanoplasty", "validate": {"required": true, "minLength": 3}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_head_neck?.ent && data.ent_procedure_known === 'specify';"}, {"key": "dental_header", "html": "</br><h4>Dental/Oral & Maxillofacial</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.procedure_body_area_head_neck?.dental;"}, {"key": "dental_procedure_known", "type": "radio", "input": true, "label": "Can you name the dental/oral procedure?", "values": [{"label": "Yes - I can specify", "value": "specify"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_head_neck?.dental;"}, {"key": "dental_procedure_text", "type": "textfield", "input": true, "label": "What is the name of the dental/oral procedure?", "placeholder": "e.g., wisdom tooth extraction, implants, jaw surgery", "validate": {"required": true, "minLength": 3}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_head_neck?.dental && data.dental_procedure_known === 'specify';"}, {"key": "breast_header", "html": "</br><h4>Breast/Chest</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.procedure_body_area_torso?.breast;"}, {"key": "breast_procedure_known", "type": "radio", "input": true, "label": "Can you name the breast/chest procedure?", "values": [{"label": "Yes - I can specify", "value": "specify"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_torso?.breast;"}, {"key": "breast_procedure_text", "type": "textfield", "input": true, "label": "What is the name of the breast/chest procedure?", "placeholder": "e.g., augmentation, reduction, lumpectomy, mastectomy, reconstruction", "validate": {"required": true, "minLength": 3}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_torso?.breast && data.breast_procedure_known === 'specify';"}, {"key": "abdomen_header", "html": "</br><h4>Abdomen/General Surgery</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.procedure_body_area_torso?.abdomen;"}, {"key": "abdomen_procedure_known", "type": "radio", "input": true, "label": "Can you name the abdominal procedure?", "values": [{"label": "Yes - I can specify", "value": "specify"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_torso?.abdomen;"}, {"key": "abdomen_procedure_text", "type": "textfield", "input": true, "label": "What is the name of the abdominal procedure?", "placeholder": "e.g., gallbladder removal, hernia repair, appendectomy, bariatric surgery", "validate": {"required": true, "minLength": 3}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_torso?.abdomen && data.abdomen_procedure_known === 'specify';"}, {"key": "gyne_header", "html": "</br><h4>Pelvis/Gynecology</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.procedure_body_area_pelvis_gu?.gyne;"}, {"key": "gyne_procedure_known", "type": "radio", "input": true, "label": "Can you name the gynecology procedure?", "values": [{"label": "Yes - I can specify", "value": "specify"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_pelvis_gu?.gyne;"}, {"key": "gyne_procedure_text", "type": "textfield", "input": true, "label": "What is the name of the gynecology procedure?", "placeholder": "e.g., C-section, hysterectomy, hysteroscopy/D&C, tubal ligation", "validate": {"required": true, "minLength": 3}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_pelvis_gu?.gyne && data.gyne_procedure_known === 'specify';"}, {"key": "urology_header", "html": "</br><h4>Urology</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.procedure_body_area_pelvis_gu?.urology;"}, {"key": "urology_procedure_known", "type": "radio", "input": true, "label": "Can you name the urology procedure?", "values": [{"label": "Yes - I can specify", "value": "specify"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_pelvis_gu?.urology;"}, {"key": "urology_procedure_text", "type": "textfield", "input": true, "label": "What is the name of the urology procedure?", "placeholder": "e.g., vasectomy, TURP, prostatectomy, cystoscopy/lithotripsy", "validate": {"required": true, "minLength": 3}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_pelvis_gu?.urology && data.urology_procedure_known === 'specify';"}, {"key": "ortho_header", "html": "</br><h4>Orthopedic (Bone/Joint)</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.procedure_body_area_musculoskeletal?.ortho;"}, {"key": "ortho_procedure_known", "type": "radio", "input": true, "label": "Can you name the orthopedic procedure?", "values": [{"label": "Yes - I can specify", "value": "specify"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_musculoskeletal?.ortho;"}, {"key": "ortho_procedure_text", "type": "textfield", "input": true, "label": "What is the name of the orthopedic procedure?", "placeholder": "e.g., knee arthroscopy, ACL reconstruction, total hip replacement", "validate": {"required": true, "minLength": 3}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_musculoskeletal?.ortho && data.ortho_procedure_known === 'specify';"}, {"key": "spine_header", "html": "</br><h4>Spine/Neurosurgery</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.procedure_body_area_musculoskeletal?.spine;"}, {"key": "spine_procedure_known", "type": "radio", "input": true, "label": "Can you name the spine/neurosurgery procedure?", "values": [{"label": "Yes - I can specify", "value": "specify"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_musculoskeletal?.spine;"}, {"key": "spine_procedure_text", "type": "textfield", "input": true, "label": "What is the name of the spine/neurosurgery procedure?", "placeholder": "e.g., discectomy, laminectomy, fusion", "validate": {"required": true, "minLength": 3}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_musculoskeletal?.spine && data.spine_procedure_known === 'specify';"}, {"key": "cardiac_header", "html": "</br><h4>Cardiac/Heart</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.procedure_body_area_torso?.cardiac;"}, {"key": "cardiac_procedure_known", "type": "radio", "input": true, "label": "Can you name the heart procedure?", "values": [{"label": "Yes - I can specify", "value": "specify"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_torso?.cardiac;"}, {"key": "cardiac_procedure_text", "type": "textfield", "input": true, "label": "What is the name of the heart procedure?", "placeholder": "e.g., CABG, valve surgery, pacemaker/ICD insertion", "validate": {"required": true, "minLength": 3}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_torso?.cardiac && data.cardiac_procedure_known === 'specify';"}, {"key": "thoracic_header", "html": "</br><h4><PERSON><PERSON><PERSON>/Lungs</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.procedure_body_area_torso?.thoracic;"}, {"key": "thoracic_procedure_known", "type": "radio", "input": true, "label": "Can you name the lung/thoracic procedure?", "values": [{"label": "Yes - I can specify", "value": "specify"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_torso?.thoracic;"}, {"key": "thoracic_procedure_text", "type": "textfield", "input": true, "label": "What is the name of the lung/thoracic procedure?", "placeholder": "e.g., VATS/wedge, lobectomy", "validate": {"required": true, "minLength": 3}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_body_area_torso?.thoracic && data.thoracic_procedure_known === 'specify';"}, {"key": "procedure_date_known", "type": "radio", "input": true, "label": "Do you have a scheduled procedure date?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No/not yet scheduled", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:"}, {"key": "procedure_date", "type": "datetime", "input": true, "label": "Procedure date", "format": "yyyy-MM-dd", "widget": {"type": "calendar"}, "tableView": true, "enableTime": false, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_date_known === 'yes';"}, {"key": "documents_header", "html": "</br><h4>Upload Requests/Forms</h4><p>If your surgeon or hospital provided a list of required tests or a pre-admission form, upload it here.</p>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "skip_upload_forms", "type": "checkbox", "input": true, "label": "I do not have the paperwork right now", "tableView": true, "defaultValue": false, "confirm_label": "Phase that summarizes the question:"}, {"key": "preop_forms_upload", "url": "/app/q/{qpk}/formio-files/{pk}/", "type": "file", "image": true, "input": true, "label": "Upload your pre-admission or surgeon request", "webcam": true, "capture": "user", "storage": "url", "multiple": true, "fileTypes": [{"label": "", "value": ""}], "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = !data.skip_upload_forms;", "validateWhenHidden": false}, {"key": "prior_testing_header", "html": "</br><h4>Recent Testing</h4>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "recent_labs_90d", "type": "radio", "input": true, "label": "Have you had blood tests done in the last 90 days?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "unsure"}], "tableView": true, "confirm_label": "Phase that summarizes the question:"}, {"key": "recent_ekg_6m", "type": "radio", "input": true, "label": "Have you had a heart test (ECG) in the last 6 months?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "unsure"}], "tableView": true, "confirm_label": "Phase that summarizes the question:"}, {"key": "preop_tests_header", "html": "</br><h4>Which Tests Were Requested?</h4><p>If you know which tests the surgeon/hospital requested, select them below. If you're not sure, choose “I'm not sure” and upload any forms you have.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.procedure_category && ((data.procedure_body_area_head_neck && Object.values(data.procedure_body_area_head_neck).some(<PERSON><PERSON><PERSON>)) || (data.procedure_body_area_torso && Object.values(data.procedure_body_area_torso).some(<PERSON><PERSON><PERSON>)) || (data.procedure_body_area_pelvis_gu && Object.values(data.procedure_body_area_pelvis_gu).some(<PERSON><PERSON><PERSON>)) || (data.procedure_body_area_musculoskeletal && Object.values(data.procedure_body_area_musculoskeletal).some(<PERSON><PERSON><PERSON>)) || data.procedure_body_area_other_text);"}, {"key": "requested_tests_known", "type": "radio", "input": true, "label": "Do you know which tests are required?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No/I'm not sure", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.procedure_category && ((data.procedure_body_area_head_neck && Object.values(data.procedure_body_area_head_neck).some(<PERSON><PERSON><PERSON>)) || (data.procedure_body_area_torso && Object.values(data.procedure_body_area_torso).some(<PERSON><PERSON><PERSON>)) || (data.procedure_body_area_pelvis_gu && Object.values(data.procedure_body_area_pelvis_gu).some(<PERSON><PERSON><PERSON>)) || (data.procedure_body_area_musculoskeletal && Object.values(data.procedure_body_area_musculoskeletal).some(<PERSON><PERSON><PERSON>)) || data.procedure_body_area_other_text);"}, {"key": "routine_tests_header", "html": "<h5>Routine Blood Work & Tests</h5><ul style=\"margin-top:6px\"><li>Some surgeons ask for metabolic testing (e.g., A1C, thyroid, electrolytes).</li><li>If your surgeon asked for something not listed, choose 'Other routine tests' and type it in.</li><li>If you aren't sure what they need, write 'I'm not sure.'</li></ul>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.requested_tests_known === 'yes';"}, {"key": "routine_tests", "type": "selectboxes", "input": true, "label": "Which routine tests were requested? (Check all that apply)", "values": [{"label": "Complete blood count (CBC) - checks blood cells", "value": "cbc"}, {"label": "Kidney function tests - checks how well kidneys work", "value": "kidney"}, {"label": "Blood salts test (sodium, potassium)", "value": "electrolytes"}, {"label": "Blood sugar or diabetes test", "value": "glucose_a1c"}, {"label": "Blood clotting tests - checks how fast blood clots", "value": "coag"}, {"label": "Blood type & antibody screen", "value": "type_screen"}, {"label": "Pregnancy test", "value": "hcg"}, {"label": "Heart test (ECG)", "value": "ecg"}, {"label": "Nose or skin swab for bacteria (MRSA)", "value": "mrsa"}, {"label": "Other routine tests", "value": "other"}], "tableView": true, "confirm_label": "Routine tests requested:", "customConditional": "show = data.requested_tests_known === 'yes';"}, {"key": "routine_tests_other_text", "type": "textfield", "input": true, "label": "Please specify what other routine tests were requested:", "validate": {"required": true}, "tableView": true, "placeholder": "e.g., thyroid function, vitamin levels, liver function, etc.", "confirm_label": "Other routine tests specified:", "customConditional": "show = data.requested_tests_known === 'yes' && data.routine_tests && data.routine_tests.other;"}, {"key": "infectious_disease_header", "html": "<h5>Infectious Disease Screening</h5><p>Some surgeries require testing for infections that can be passed from person to person.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.requested_tests_known === 'yes';"}, {"key": "infectious_disease_tests", "type": "selectboxes", "input": true, "label": "Which infection screening tests were requested? (Check all that apply)", "values": [{"label": "HIV test", "value": "hiv"}, {"label": "Hepatitis B test (liver infection)", "value": "hep_b"}, {"label": "Hepatitis C test (liver infection)", "value": "hep_c"}, {"label": "Syphilis test (sexually transmitted infection)", "value": "syphilis"}, {"label": "None of these were requested", "value": "none"}], "tableView": true, "confirm_label": "Infection screening tests:", "customConditional": "show = data.requested_tests_known === 'yes';"}, {"key": "other_tests_header", "html": "<h5>Other Tests</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.requested_tests_known === 'yes';"}, {"key": "other_tests_requested", "type": "radio", "input": true, "label": "Were any other tests requested that aren't listed above?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "confirm_label": "Other tests requested:", "customConditional": "show = data.requested_tests_known === 'yes';"}, {"key": "other_tests_text", "type": "textfield", "input": true, "label": "Please list the other tests:", "validate": {"required": true}, "tableView": true, "placeholder": "e.g., thyroid function, vitamin levels, etc.", "confirm_label": "Other tests specified:", "customConditional": "show = data.requested_tests_known === 'yes' && data.other_tests_requested === 'yes';"}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';", "confirm_label": "Phase that summarizes the question:"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-preop':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-req','appointment-intake','edit-intake']"}]}