{"components": [{"key": "mobile_table_css", "tag": "style", "type": "htmlelement", "content": "@media (max-width:600px){.flaretbl table thead,.flaretbl .datagrid-table thead{display:none!important;}.flaretbl table tbody tr{display:block;margin:0 0 1rem;border:1px solid #e0e0e0;border-radius:4px;}.flaretbl table tbody td{display:block;width:100%;border:none;padding:6px 12px;white-space:normal;}}"}, {"key": "heading_treatment_preferences_kp", "html": "<h1 class=\"text-center\">Your KP Treatment Preferences</h1>", "type": "content", "input": false}, {"key": "preferences_callout_kp", "tag": "div", "type": "htmlelement", "input": false, "content": "<div style=\"border:1px solid #dee2e6;border-radius:6px;padding:0.9rem;margin-bottom:1rem;background:#f8f9fa;\"><ul style=\"margin:0 0 0 1rem;line-height:1.5;\"><li>Select options that fit <em>your</em> budget, routine and comfort level.</li><li><strong>Prices shown</strong> are Ontario estimates (ball-park only).</li><li>A TeleTest physician reviews every request; you can open a <strong>secure chat</strong> any time.</li></ul></div>"}, {"key": "must_avoid_kp", "type": "selectboxes", "label": "Anything you definitely want to <strong>avoid</strong>? (select all that apply)", "values": [{"label": "Strong exfoliating acids", "value": "avoid_acids"}, {"label": "Retinoids (vitamin A creams)", "value": "avoid_retinoid"}, {"label": "Fragrance", "value": "avoid_fragrance"}, {"label": "Nothing in particular", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "optionsLabelPosition": "right", "confirm_label": "KP options to avoid:"}, {"key": "kp_body_areas", "type": "selectboxes", "label": "Which area(s) do you treat (or plan to treat) for KP? (Select all that apply)", "values": [{"label": "Upper arms", "value": "arms"}, {"label": "Forearms", "value": "forearms"}, {"label": "Thighs", "value": "thighs"}, {"label": "<PERSON><PERSON>", "value": "calves"}, {"label": "Buttocks", "value": "buttocks"}, {"label": "Cheeks / Face", "value": "face"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "KP treatment areas:"}, {"key": "shared_kp_values", "type": "hidden", "defaultValue": [{"label": "<span style='color:#28a745;font-weight:bold;'>Tazarotene 0.045 % lotion (Arazlo™)</span>", "value": "taz045"}, {"label": "Tretinoin 0.05 % cream", "value": "tret05"}, {"label": "Adapalene 0.1 % gel", "value": "adap01"}, {"label": "Azelaic acid 15 % gel", "value": "azelaic15"}, {"label": "Lactic-acid 12 % lotion", "value": "lactic12"}, {"label": "Salicylic-acid 2 % lotion", "value": "sal2"}, {"label": "Drug-free (just moisturiser)", "value": "drug_free"}, {"label": "Other (type below)", "value": "other"}]}, {"key": "heading_arms_flare", "html": "<br><h5><strong>Arms (upper / forearms) – Active Treatment</strong></h5>", "type": "content", "input": false, "customConditional": "show = data.kp_body_areas?.arms || data.kp_body_areas?.forearms;"}, {"key": "arms_flare", "type": "radio", "defaultValue": "taz045", "label": "Which product will you use <strong>during an active phase</strong>?<br><small>Apply nightly for about 6-8 weeks, then switch to maintenance.</small>", "values": "values = instance.root._data.shared_kp_values", "validate": {"required": true}, "confirm_label": "Arms – Active treatment:", "customConditional": "show = data.kp_body_areas?.arms || data.kp_body_areas?.forearms;"}, {"key": "arms_flare_other", "type": "textfield", "label": "Other product", "validate": {"required": true}, "customConditional": "show = data.arms_flare==='other';"}, {"key": "heading_arms_maint", "html": "<h5 style=\"margin-top:1rem;\"><strong>Arms – Maintenance</strong></h5>", "type": "content", "input": false, "customConditional": "show = data.kp_body_areas?.arms || data.kp_body_areas?.forearms;"}, {"key": "arms_maint", "type": "radio", "defaultValue": "taz045", "label": "Which product will you use for <strong>maintenance</strong>?<br><small>Apply 3-4× weekly once skin is smooth.</small>", "values": "values = instance.root._data.shared_kp_values", "validate": {"required": true}, "confirm_label": "Arms – Maintenance:", "customConditional": "show = data.kp_body_areas?.arms || data.kp_body_areas?.forearms;"}, {"key": "arms_maint_other", "type": "textfield", "label": "Other product", "validate": {"required": true}, "customConditional": "show = data.arms_maint==='other';"}, {"key": "heading_legs_flare", "html": "<br><h5><strong>Thighs / Calves – Active Treatment</strong></h5>", "type": "content", "input": false, "customConditional": "show = data.kp_body_areas?.thighs || data.kp_body_areas?.calves;"}, {"key": "legs_flare", "type": "radio", "defaultValue": "taz045", "label": "Which product will you use during an active phase?<br><small>Apply nightly for about 6-8 weeks, then switch to maintenance.</small>", "values": "values = instance.root._data.shared_kp_values", "validate": {"required": true}, "confirm_label": "Legs – Active treatment:", "customConditional": "show = data.kp_body_areas?.thighs || data.kp_body_areas?.calves;"}, {"key": "legs_flare_other", "type": "textfield", "label": "Other product", "validate": {"required": true}, "customConditional": "show = data.legs_flare==='other';"}, {"key": "heading_legs_maint", "html": "<h5 style=\"margin-top:1rem;\"><strong>Thighs / Calves – Maintenance</strong></h5>", "type": "content", "input": false, "customConditional": "show = data.kp_body_areas?.thighs || data.kp_body_areas?.calves;"}, {"key": "legs_maint", "type": "radio", "defaultValue": "taz045", "label": "Which product will you use for maintenance?<br><small>Apply 3-4× weekly once skin is smooth.</small>", "values": "values = instance.root._data.shared_kp_values", "validate": {"required": true}, "confirm_label": "Legs – Maintenance:", "customConditional": "show = data.kp_body_areas?.thighs || data.kp_body_areas?.calves;"}, {"key": "legs_maint_other", "type": "textfield", "label": "Other product", "validate": {"required": true}, "customConditional": "show = data.legs_maint==='other';"}, {"key": "heading_butt_flare", "html": "<br><h5><strong>Buttocks – Active Treatment</strong></h5>", "type": "content", "input": false, "customConditional": "show = data.kp_body_areas?.buttocks;"}, {"key": "butt_flare", "type": "radio", "defaultValue": "taz045", "label": "Which product will you use during an active phase?<br><small>Apply nightly for about 6-8 weeks, then switch to maintenance.</small>", "values": "values = instance.root._data.shared_kp_values", "validate": {"required": true}, "confirm_label": "Buttocks – Active treatment:", "customConditional": "show = data.kp_body_areas?.buttocks;"}, {"key": "butt_flare_other", "type": "textfield", "label": "Other product", "validate": {"required": true}, "customConditional": "show = data.butt_flare==='other';"}, {"key": "heading_butt_maint", "html": "<h5 style=\"margin-top:1rem;\"><strong>Buttocks – Maintenance</strong></h5>", "type": "content", "input": false, "customConditional": "show = data.kp_body_areas?.buttocks;"}, {"key": "butt_maint", "type": "radio", "defaultValue": "taz045", "label": "Which product will you use for maintenance?<br><small>Apply 3-4× weekly once skin is smooth.</small>", "values": "values = instance.root._data.shared_kp_values", "validate": {"required": true}, "confirm_label": "Buttocks – Maintenance:", "customConditional": "show = data.kp_body_areas?.buttocks;"}, {"key": "butt_maint_other", "type": "textfield", "label": "Other product", "validate": {"required": true}, "customConditional": "show = data.butt_maint==='other';"}, {"key": "heading_face_flare_kp", "html": "<br><h5><strong>Cheeks / Face – Active Treatment</strong></h5>", "type": "content", "input": false, "customConditional": "show = data.kp_body_areas?.face;"}, {"key": "kp_face_flare", "type": "radio", "defaultValue": "taz045", "label": "Which product will you use during an active phase?<br><small>Apply nightly for about 6-8 weeks, then switch to maintenance.</small>", "values": "values = instance.root._data.shared_kp_values", "validate": {"required": true}, "confirm_label": "Face – Active treatment:", "customConditional": "show = data.kp_body_areas?.face;"}, {"key": "kp_face_flare_other", "type": "textfield", "label": "Other product", "validate": {"required": true}, "customConditional": "show = data.kp_face_flare==='other';"}, {"key": "heading_face_maint_kp", "html": "<h5 style=\"margin-top:1rem;\"><strong>Cheeks / Face – Maintenance</strong></h5>", "type": "content", "input": false, "customConditional": "show = data.kp_body_areas?.face;"}, {"key": "kp_face_maint", "type": "radio", "defaultValue": "taz045", "label": "Which product will you use for maintenance?<br><small>Apply 3-4× weekly once skin is smooth.</small>", "values": "values = instance.root._data.shared_kp_values", "validate": {"required": true}, "confirm_label": "Face – Maintenance:", "customConditional": "show = data.kp_body_areas?.face;"}, {"key": "kp_face_maint_other", "type": "textfield", "label": "Other product", "validate": {"required": true}, "customConditional": "show = data.kp_face_maint==='other';"}]}