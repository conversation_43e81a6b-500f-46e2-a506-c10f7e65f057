{"components": [{"key": "heading_past_treatments_rosacea", "html": "</br><h4>Past Rosacea Treatments</h4>", "type": "content", "input": false, "tableView": false, "refreshOnChange": false}, {"key": "used_prescription_past_rosacea", "type": "radio", "input": true, "label": "Have you used prescription rosacea medications (topical or oral) in the past?", "confirm_label": "Used prescription rosacea medications", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "refreshOnChange": false}, {"key": "past_treatment_effectiveness_rosacea", "type": "radio", "input": true, "label": "How well did past prescription treatments work overall?", "confirm_label": "Past treatment effectiveness", "values": [{"label": "Very effective - symptoms well controlled", "value": "very_effective"}, {"label": "Helped but didn't fully control symptoms", "value": "partial"}, {"label": "Not effective", "value": "ineffective"}, {"label": "Not sure / varied", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "refreshOnChange": false, "customConditional": "show = data.used_prescription_past_rosacea === 'yes';"}, {"key": "past_rx_types", "type": "selectboxes", "input": true, "label": "Which prescription types have you used? (Select all that apply)", "confirm_label": "Prescription types used", "values": [{"label": "Topical creams / gels", "value": "topical"}, {"label": "Oral medicines (antibiotics)", "value": "oral"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "refreshOnChange": false, "customConditional": "show = data.used_prescription_past_rosacea === 'yes' && !!data.past_treatment_effectiveness_rosacea;"}, {"key": "heading_topical_selected", "html": "<br><h4>Topical products you've used</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.past_rx_types && data.past_rx_types.topical === true;"}, {"key": "rosacea_rx_topical_grid", "type": "<PERSON><PERSON><PERSON>", "label": "Add each topical product you've used:", "addAnother": "+ Add Another", "openWhenEmpty": true, "defaultOpen": true, "displayAsTable": false, "useRowTemplate": true, "tableView": false, "refreshOnChange": false, "removeRow": true, "customConditional": "show = data.past_rx_types && data.past_rx_types.topical;", "rowTemplate": "<div class='card shadow-sm w-100'><div class='card-body py-2 px-3'><div class='row small'><div class='col-sm-3'><strong>{{ data.rx_name === 'other' ? data.rx_other_name : _.startCase(data.rx_name.replace(/_/g,' ')) }}</strong></div><div class='col-sm-3'>{{ _.join(_.map(_.keys(_.pickBy(data.rx_where,Boolean)), _.startCase), ', ') }}</div><div class='col-sm-1'>{{ data.rx_current==='current' ? 'Yes' : 'Past' }}</div><div class='col-sm-2'>{{ (data.rx_effect || '').replace('_',' ') | title }}</div><div class='col-sm-1'>{{ ({qd:'1×',bid:'2×',qod:'QOD',prn:'PRN',unsure:'N/S'}[data.rx_freq] || '') }}</div><div class='col-sm-2'>{{ ({le4w:'≤4 w','1_3m':'1-3 m','3_6m':'3-6 m','6_12m':'6-12 m','gt12m':'12 m+'}[data.rx_longest] || '') }}</div></div></div></div>", "components": [{"key": "rx_name", "type": "select", "label": "Topical product", "widget": "html5", "validate": {"required": true}, "data": {"values": [{"label": "Metronidazole 0.75% gel - MetroGel", "value": "metronidazole_075_gel"}, {"label": "Metronidazole 0.75% cream - MetroCream", "value": "metronidazole_075_cream"}, {"label": "Metronidazole 1% cream - Noritate", "value": "metronidazole_1_gel"}, {"label": "Azelaic acid 15% gel - Finacea", "value": "azelaic_15_gel"}, {"label": "Sodium sulfacetamide/sulfur (generic/compounded)", "value": "sulfacetamide_sulfur"}, {"label": "Ivermectin 1% cream - Rosiver", "value": "ivermectin_cream"}, {"label": "Tretinoin 0.025% cream/gel", "value": "tretinoin_0025"}, {"label": "Tretinoin 0.05% cream/gel", "value": "tretinoin_005"}, {"label": "Adapalene 0.1% gel", "value": "adapalene_01"}, {"label": "Other topical Rx", "value": "other"}]}}, {"key": "rx_other_name", "type": "textfield", "label": "Other product", "validate": {"required": true}, "customConditional": "show = row && row.rx_name === 'other';"}, {"key": "rx_where", "type": "selectboxes", "label": "Main area applied", "inputType": "checkbox", "values": [{"label": "Central face (nose, cheeks, chin, forehead)", "value": "central_face"}, {"label": "Cheeks only", "value": "cheeks"}, {"label": "Nose only", "value": "nose"}, {"label": "Forehead only", "value": "forehead"}, {"label": "Chin only", "value": "chin"}, {"label": "Eyelids", "value": "eyelids"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}}, {"key": "rx_where_other", "type": "textfield", "label": "Other area", "validate": {"required": true}, "customConditional": "show = row && row.rx_where && row.rx_where.other;"}, {"key": "rx_current", "type": "radio", "label": "Using", "inline": true, "values": [{"label": "Currently", "value": "current"}, {"label": "Past", "value": "past"}], "validate": {"required": true}}, {"key": "stop_reason", "type": "select", "label": "Why did you stop this medicine?", "widget": "html5", "validate": {"required": true}, "customConditional": "show = row && row.rx_current === 'past';", "data": {"values": [{"label": "Symptoms controlled / no longer needed", "value": "cleared"}, {"label": "Side-effects", "value": "sidefx"}, {"label": "Didn't help enough", "value": "ineff"}, {"label": "Cost / insurance coverage", "value": "cost"}, {"label": "Pregnancy / breastfeeding", "value": "preg"}, {"label": "Other reason", "value": "other"}]}}, {"key": "stop_sidefx_list", "type": "selectboxes", "label": "Which side-effects did you notice?", "inputType": "checkbox", "values": [{"label": "Dryness / peeling", "value": "dry"}, {"label": "Redness / irritation", "value": "red"}, {"label": "Burning / stinging", "value": "burn"}, {"label": "Increased sun-sensitivity", "value": "sun"}, {"label": "Headache / dizziness", "value": "headache"}, {"label": "Upset stomach", "value": "gi"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "customConditional": "show = row && row.stop_reason === 'sidefx';"}, {"key": "stop_sidefx_other_detail", "type": "textfield", "label": "Other side-effect:", "validate": {"required": true}, "customConditional": "show = row && row.stop_sidefx_list && row.stop_sidefx_list.other;"}, {"key": "stop_other_detail", "type": "textfield", "label": "Please specify other reason:", "validate": {"required": true}, "customConditional": "show = row && row.stop_reason === 'other';"}, {"key": "rx_last_used", "type": "select", "label": "When did you last use it?", "widget": "html5", "validate": {"required": true}, "customConditional": "show = row && row.rx_current === 'past';", "data": {"values": [{"label": "≤ 1 week ago", "value": "le1w"}, {"label": "1-4 weeks ago", "value": "1_4w"}, {"label": "1-3 months ago", "value": "1_3m"}, {"label": "3-6 months ago", "value": "3_6m"}, {"label": "> 6 months ago", "value": "gt6m"}, {"label": "Not sure", "value": "unsure"}]}}, {"key": "rx_effect", "type": "select", "label": "Effect", "widget": "html5", "validate": {"required": true}, "data": {"values": [{"label": "Controlled rosacea symptoms", "value": "cleared"}, {"label": "Helped somewhat", "value": "partial"}, {"label": "No change", "value": "none"}, {"label": "Made it worse", "value": "worse"}, {"label": "Not sure", "value": "unsure"}]}}, {"key": "rx_freq", "type": "select", "label": "Frequency", "widget": "html5", "validate": {"required": true}, "data": {"values": [{"label": "Once daily", "value": "qd"}, {"label": "Twice daily", "value": "bid"}, {"label": "Every other day", "value": "qod"}, {"label": "PRN / occasionally", "value": "prn"}, {"label": "Not sure", "value": "unsure"}]}}, {"key": "rx_longest", "type": "select", "label": "Longest single stretch you used it <em>without breaks</em>:", "widget": "html5", "validate": {"required": true}, "data": {"values": [{"label": "≤ 4 weeks", "value": "le4w"}, {"label": "1 month", "value": "1m"}, {"label": "2 months", "value": "2m"}, {"label": "3 months", "value": "3m"}, {"label": "4 months", "value": "4m"}, {"label": "5 months", "value": "5m"}, {"label": "6 months", "value": "6m"}, {"label": "7 months", "value": "7m"}, {"label": "8 months", "value": "8m"}, {"label": "9 months", "value": "9m"}, {"label": "10 months", "value": "10m"}, {"label": "11 months", "value": "11m"}, {"label": "12 months", "value": "12m"}, {"label": "1-2 years", "value": "1_2y"}, {"label": "2-3 years", "value": "2_3y"}, {"label": "3-5 years", "value": "3_5y"}, {"label": "> 5 years", "value": "gt5y"}, {"label": "Not sure", "value": "unsure"}]}}, {"key": "rx_form", "type": "select", "label": "Form", "widget": "html5", "validate": {"required": true}, "data": {"values": [{"label": "<PERSON>el", "value": "gel"}, {"label": "Cream", "value": "cream"}, {"label": "Lotion / Foam", "value": "lotion"}, {"label": "Wash", "value": "wash"}, {"label": "Other / unsure", "value": "unsure"}]}}]}, {"key": "heading_oral_selected", "html": "<br><h4>Oral medicines you've used</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.past_rx_types && data.past_rx_types.oral === true;"}, {"key": "rosacea_rx_oral_grid", "type": "<PERSON><PERSON><PERSON>", "label": "Add each oral medication you've used:", "addAnother": "+ Add Another", "openWhenEmpty": true, "defaultOpen": true, "displayAsTable": false, "useRowTemplate": true, "tableView": false, "refreshOnChange": false, "removeRow": true, "customConditional": "show = data.past_rx_types && data.past_rx_types.oral;", "rowTemplate": "<div class='card shadow-sm w-100'><div class='card-body py-2 px-3'><div class='row small'><div class='col-sm-3'><strong>{{ data.rx_name === 'other' ? data.rx_other_name : _.startCase(data.rx_name.replace(/_/g,' ')) }}</strong></div><div class='col-sm-3'>{{ _.join(_.map(_.keys(_.pickBy(data.rx_where,Boolean)), _.startCase), ', ') }}</div><div class='col-sm-1'>{{ data.rx_current==='current' ? 'Yes' : 'Past' }}</div><div class='col-sm-2'>{{ (data.rx_effect || '').replace('_',' ') | title }}</div><div class='col-sm-1'>{{ ({qd:'1×',bid:'2×',qod:'QOD',prn:'PRN',unsure:'N/S'}[data.rx_freq] || '') }}</div><div class='col-sm-2'>{{ ({le4w:'≤4 w',1m:'1 m',2m:'2 m',3m:'3 m',4m:'4 m',5m:'5 m',6m:'6 m',7m:'7 m',8m:'8 m',9m:'9 m',10m:'10 m',11m:'11 m',12m:'12 m',1_2y:'1-2 y',2_3y:'2-3 y',3_5y:'3-5 y',gt5y:'5 y+'}[data.rx_longest] || '') }}</div></div></div></div>", "components": [{"key": "rx_name", "type": "select", "label": "Pills", "widget": "html5", "validate": {"required": true}, "data": {"values": [{"label": "Doxycycline", "value": "doxy"}, {"label": "Minocycline", "value": "mino"}, {"label": "Tetracycline", "value": "tetra"}, {"label": "Azithromycin", "value": "azith<PERSON>"}, {"label": "Erythromycin", "value": "erythro"}, {"label": "Clarithromycin", "value": "clar<PERSON><PERSON>"}, {"label": "Metronidazole", "value": "metronidazole_oral"}, {"label": "Trimethoprim-sulfamethoxazole", "value": "tmp_smx"}, {"label": "Isotretinoin (low-dose for rosacea)", "value": "iso_low"}, {"label": "Other oral medication", "value": "other"}]}}, {"key": "rx_other_name", "type": "textfield", "label": "Other product", "validate": {"required": true}, "customConditional": "show = row && row.rx_name === 'other';"}, {"key": "rx_where", "type": "selectboxes", "label": "Main area improved", "inputType": "checkbox", "values": [{"label": "Face (overall rosacea symptoms)", "value": "face"}, {"label": "Papules/pustules", "value": "papules"}, {"label": "Redness/flushing", "value": "redness"}, {"label": "Eye symptoms (ocular rosacea)", "value": "eyes"}, {"label": "Other", "value": "other"}, {"label": "None / Not sure", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}}, {"key": "rx_where_other", "type": "textfield", "label": "Other area", "validate": {"required": true}, "customConditional": "show = row && row.rx_where && row.rx_where.other;"}, {"key": "rx_current", "type": "radio", "label": "Using", "inline": true, "values": [{"label": "Currently", "value": "current"}, {"label": "Past", "value": "past"}], "validate": {"required": true}}, {"key": "stop_reason", "type": "select", "label": "Why did you stop this medicine?", "widget": "html5", "validate": {"required": true}, "customConditional": "show = row && row.rx_current === 'past';", "data": {"values": [{"label": "Symptoms controlled / no longer needed", "value": "cleared"}, {"label": "Side-effects", "value": "sidefx"}, {"label": "Didn't help enough", "value": "ineff"}, {"label": "Cost / insurance coverage", "value": "cost"}, {"label": "Pregnancy / breastfeeding", "value": "preg"}, {"label": "Other reason", "value": "other"}]}}, {"key": "stop_sidefx_list", "type": "selectboxes", "label": "Which side-effects did you notice?", "inputType": "checkbox", "values": [{"label": "Upset stomach / nausea", "value": "gi"}, {"label": "Diarrhea", "value": "diarrhea"}, {"label": "Yeast infection", "value": "yeast"}, {"label": "Sun-sensitivity", "value": "sun"}, {"label": "Headache / dizziness", "value": "headache"}, {"label": "Rash / allergy", "value": "rash"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "customConditional": "show = row && row.stop_reason === 'sidefx';"}, {"key": "stop_sidefx_other_detail", "type": "textfield", "label": "Other side-effect:", "validate": {"required": true}, "customConditional": "show = row && row.stop_sidefx_list && row.stop_sidefx_list.other;"}, {"key": "stop_other_detail", "type": "textfield", "label": "Please specify other reason:", "validate": {"required": true}, "customConditional": "show = row && row.stop_reason === 'other';"}, {"key": "rx_effect", "type": "select", "label": "Effect", "widget": "html5", "validate": {"required": true}, "data": {"values": [{"label": "Controlled rosacea symptoms", "value": "cleared"}, {"label": "Helped somewhat", "value": "partial"}, {"label": "No change", "value": "none"}, {"label": "Made it worse", "value": "worse"}, {"label": "Not sure", "value": "unsure"}]}}, {"key": "rx_freq", "type": "select", "label": "Usual dose", "widget": "html5", "validate": {"required": true}, "data": {"values": [{"label": "Once daily", "value": "qd"}, {"label": "Twice daily", "value": "bid"}, {"label": "Other / unsure", "value": "unsure"}]}}, {"key": "rx_last_used", "type": "select", "label": "When did you last use it?", "widget": "html5", "validate": {"required": true}, "customConditional": "show = row && row.rx_current === 'past';", "data": {"values": [{"label": "≤ 1 week ago", "value": "le1w"}, {"label": "1-4 weeks ago", "value": "1_4w"}, {"label": "1-3 months ago", "value": "1_3m"}, {"label": "3-6 months ago", "value": "3_6m"}, {"label": "> 6 months ago", "value": "gt6m"}, {"label": "Not sure", "value": "unsure"}]}}, {"key": "rx_longest", "type": "select", "label": "Longest single stretch you used it <em>without breaks</em>:", "widget": "html5", "validate": {"required": true}, "data": {"values": [{"label": "≤ 4 weeks", "value": "le4w"}, {"label": "1 month", "value": "1m"}, {"label": "2 months", "value": "2m"}, {"label": "3 months", "value": "3m"}, {"label": "4 months", "value": "4m"}, {"label": "5 months", "value": "5m"}, {"label": "6 months", "value": "6m"}, {"label": "7 months", "value": "7m"}, {"label": "8 months", "value": "8m"}, {"label": "9 months", "value": "9m"}, {"label": "10 months", "value": "10m"}, {"label": "11 months", "value": "11m"}, {"label": "12 months", "value": "12m"}, {"label": "1-2 years", "value": "1_2y"}, {"label": "2-3 years", "value": "2_3y"}, {"label": "3-5 years", "value": "3_5y"}, {"label": "> 5 years", "value": "gt5y"}, {"label": "Not sure", "value": "unsure"}]}}, {"key": "rx_form", "type": "select", "label": "Form", "widget": "html5", "validate": {"required": true}, "data": {"values": [{"label": "Capsule", "value": "capsule"}, {"label": "Tablet", "value": "tablet"}, {"label": "Other / unsure", "value": "unsure"}]}}]}, {"key": "rosacea_topical_summary", "type": "textarea", "input": true, "label": "Topicals (summary)", "autoExpand": true, "hidden": true, "disabled": true, "clearOnHide": false, "tableView": true, "adminFlag": true, "confirm_label": "Topical Rx history:", "calculateValue": "var pretty=function(v,map){return map[v]||v||'';}; var areaList=function(w){if(!w){return '—';} var map={central_face:'Central face',cheeks:'Cheeks',nose:'Nose',forehead:'Forehead',chin:'Chin',eyelids:'Eyelids',other:'Other',none:'None/NS'}; var ks=Object.keys(w).filter(function(k){return !!w[k];}); return ks.length?ks.map(function(k){return map[k]||k;}).join(', '):'—';}; if(!(data.past_rx_types && data.past_rx_types.topical)){ value=''; } else { var rows=data.rosacea_rx_topical_grid||[]; var nameMap={'metronidazole_075_gel':'Metronidazole 0.75% gel (MetroGel)','metronidazole_075_cream':'Metronidazole 0.75% cream (MetroCream)','metronidazole_1_gel':'Metronidazole 1% cream (Noritate)','azelaic_15_gel':'Azelaic acid 15% gel (Finacea)','sulfacetamide_sulfur':'Sodium sulfacetamide/sulfur','ivermectin_cream':'Ivermectin 1% cream (Rosiver)','tretinoin_0025':'Tretinoin 0.025%','tretinoin_005':'Tretinoin 0.05%','adapalene_01':'Adapalene 0.1%','other':'Other'}; var effMap={cleared:'Controlled',partial:'Helped somewhat',none:'No change',worse:'Worse',unsure:'Not sure'}; var freqMap={qd:'1×/day',bid:'2×/day',qod:'QOD',prn:'PRN',unsure:'N/S'}; var durMap={'le4w':'≤4 w','1m':'1 m','2m':'2 m','3m':'3 m','4m':'4 m','5m':'5 m','6m':'6 m','7m':'7 m','8m':'8 m','9m':'9 m','10m':'10 m','11m':'11 m','12m':'12 m','1_2y':'1-2 y','2_3y':'2-3 y','3_5y':'3-5 y','gt5y':'>5 y','1_3m':'1-3 m','3_6m':'3-6 m','6_12m':'6-12 m','gt12m':'>12 m'}; var lastMap={'le1w':'≤1 w','1_4w':'1-4 w','1_3m':'1-3 m','3_6m':'3-6 m','gt6m':'>6 m','unsure':'N/S'}; var sideMap={dry:'Dry/peel',red:'Red/irrit',burn:'Burn/sting',sun:'Sun-sensitive',headache:'Headache/dizzy',gi:'GI',other:'Other'}; value=rows.map(function(r){ var n=(r.rx_name==='other')?(r.rx_other_name||'Other'):pretty(r.rx_name,nameMap); var where=areaList(r.rx_where); var status=(r.rx_current==='current')?'Current':'Past'; var eff=pretty(r.rx_effect,effMap); var freq=pretty(r.rx_freq,freqMap); var dur=pretty(r.rx_longest,durMap); var parts=[n,'—',status]; parts.push('| Area: '+where); if(r.rx_effect){parts.push('| Effect: '+eff);} if(r.rx_freq){parts.push('| Freq: '+freq);} if(r.rx_longest){parts.push('| Longest: '+dur);} if(r.rx_current==='past'){ if(r.stop_reason){ var srMap={cleared:'Controlled/no longer needed',sidefx:'Side-effects',ineff:\"Didn't help\",cost:'Cost/coverage',preg:'Preg/breastfeeding',other:'Other'}; parts.push('| Stopped: '+pretty(r.stop_reason,srMap)); if(r.stop_reason==='sidefx' && r.stop_sidefx_list){ var ks=Object.keys(r.stop_sidefx_list).filter(function(k){return !!r.stop_sidefx_list[k];}); if(ks.length){ parts.push('('+ks.map(function(k){return sideMap[k]||k;}).join(', ')+')'); } } if(r.stop_reason==='other' && r.stop_other_detail){ parts.push('('+r.stop_other_detail+')'); } if(r.rx_last_used){ parts.push('| Last used: '+pretty(r.rx_last_used,lastMap)); } } } return parts.join(' '); }).join('\\n'); }"}, {"key": "rosacea_oral_summary", "type": "textarea", "input": true, "label": "Orals (summary)", "autoExpand": true, "hidden": true, "disabled": true, "clearOnHide": false, "tableView": true, "adminFlag": true, "confirm_label": "Oral Rx history:", "calculateValue": "var pretty=function(v,map){return map[v]||v||'';}; var areaList=function(w){if(!w){return '—';} var map={face:'Face',papules:'Papules/pustules',redness:'Redness/flushing',eyes:'Eyes',other:'Other',none:'None/NS'}; var ks=Object.keys(w).filter(function(k){return !!w[k];}); return ks.length?ks.map(function(k){return map[k]||k;}).join(', '):'—';}; if(!(data.past_rx_types && data.past_rx_types.oral)){ value=''; } else { var rows=data.rosacea_rx_oral_grid||[]; var nameMap={doxy:'Doxycycline',mino:'Minocycline',tetra:'Tetracycline',azithro:'Azithromycin',erythro:'Erythromycin',clarithro:'Clarithromycin',metronidazole_oral:'Metronidazole',tmp_smx:'TMP-SMX',iso_low:'Isotretinoin (low-dose)',other:'Other'}; var effMap={cleared:'Controlled',partial:'Helped somewhat',none:'No change',worse:'Worse',unsure:'Not sure'}; var freqMap={qd:'1×/day',bid:'2×/day',unsure:'N/S'}; var durMap={'le4w':'≤4 w','1m':'1 m','2m':'2 m','3m':'3 m','4m':'4 m','5m':'5 m','6m':'6 m','7m':'7 m','8m':'8 m','9m':'9 m','10m':'10 m','11m':'11 m','12m':'12 m','1_2y':'1-2 y','2_3y':'2-3 y','3_5y':'3-5 y','gt5y':'>5 y'}; var lastMap={'le1w':'≤1 w','1_4w':'1-4 w','1_3m':'1-3 m','3_6m':'3-6 m','gt6m':'>6 m','unsure':'N/S'}; var sideMap={gi:'GI upset',diarrhea:'Diarrhea',yeast:'Yeast infxn',sun:'Sun-sensitive',headache:'Headache/dizzy',rash:'Rash/allergy',other:'Other'}; value=rows.map(function(r){ var n=(r.rx_name==='other')?(r.rx_other_name||'Other'):pretty(r.rx_name,nameMap); var where=areaList(r.rx_where); var status=(r.rx_current==='current')?'Current':'Past'; var eff=pretty(r.rx_effect,effMap); var freq=pretty(r.rx_freq,freqMap); var dur=pretty(r.rx_longest,durMap); var parts=[n,'—',status]; parts.push('| Improved: '+where); if(r.rx_effect){parts.push('| Effect: '+eff);} if(r.rx_freq){parts.push('| Dose: '+freq);} if(r.rx_longest){parts.push('| Longest: '+dur);} if(r.rx_current==='past'){ if(r.stop_reason){ var srMap={cleared:'Controlled/no longer needed',sidefx:'Side-effects',ineff:\"Didn't help\",cost:'Cost/coverage',preg:'Preg/breastfeeding',other:'Other'}; parts.push('| Stopped: '+pretty(r.stop_reason,srMap)); if(r.stop_reason==='sidefx' && r.stop_sidefx_list){ var ks=Object.keys(r.stop_sidefx_list).filter(function(k){return !!r.stop_sidefx_list[k];}); if(ks.length){ parts.push('('+ks.map(function(k){return sideMap[k]||k;}).join(', ')+')'); } } if(r.stop_reason==='other' && r.stop_other_detail){ parts.push('('+r.stop_other_detail+')'); } if(r.rx_last_used){ parts.push('| Last used: '+pretty(r.rx_last_used,lastMap)); } } } return parts.join(' '); }).join('\\n'); }"}, {"key": "rosacea_past_rx_summary", "type": "textarea", "input": true, "label": "Past rosacea Rx (combined summary)", "autoExpand": true, "hidden": true, "disabled": true, "clearOnHide": false, "tableView": true, "adminFlag": true, "confirm_label": "Past rosacea Rx summary:", "calculateValue": "var top=(data.rosacea_topical_summary||'').trim(); var oral=(data.rosacea_oral_summary||'').trim(); if(!(data.used_prescription_past_rosacea==='yes')){ value=''; } else { var out=[]; if(top){ out.push('Topicals:\\n'+top); } if(oral){ out.push('Orals:\\n'+oral); } value=out.join('\\n\\n'); }"}]}