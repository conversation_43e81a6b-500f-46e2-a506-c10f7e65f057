{"components": [{"key": "heading_intro", "type": "content", "html": "<h2><strong>STD/STI Follow-Up</strong></h2><p>Please answer the following questions so we can confirm the safest next steps.</p>", "input": false}, {"key": "heading_flagged_assays", "type": "content", "html": "<h4>Flagged Results</h4><p>These are the infections your test may have detected.</p>", "input": false}, {"key": "assay_summary", "type": "textfield", "input": true, "label": "Flagged <PERSON><PERSON>s", "hidden": true, "disabled": true, "multiple": true}, {"key": "heading_abnormal_results_ack", "type": "content", "input": false, "label": "Content", "html": "</br><h4>Abnormal Test Results</h4><p>Please confirm that you understand the result(s) that tested positive.</p>", "tableView": false, "refreshOnChange": false, "customConditional": "show = (data.flagged_aks && data.flagged_aks.length > 0);"}, {"key": "ct_result_ack", "type": "radio", "input": true, "label": "Please confirm you have reviewed and acknowledge that your Chlamydia (CT) test result is <strong>positive</strong>.", "values": [{"label": "I acknowledge this positive result", "value": true}, {"label": "I do not acknowledge", "value": false}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Chlamydia result acknowledged:", "customConditional": "show = _.includes(data.flagged_aks || [], 'CT');"}, {"key": "gc_result_ack", "type": "radio", "input": true, "label": "Please confirm you have reviewed and acknowledge that your Gonorrhea (GC) test result is <strong>positive</strong>.", "values": [{"label": "I acknowledge this positive result", "value": true}, {"label": "I do not acknowledge", "value": false}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Gonorrhea result acknowledged:", "customConditional": "show = _.includes(data.flagged_aks || [], 'GC');"}, {"key": "trich_result_ack", "type": "radio", "input": true, "label": "Please confirm you have reviewed and acknowledge that your Trichomonas (Trich) test result is <strong>positive</strong>.", "values": [{"label": "I acknowledge this positive result", "value": true}, {"label": "I do not acknowledge", "value": false}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Trichomonas result acknowledged:", "customConditional": "show = _.includes(data.flagged_aks || [], 'TRICH');"}, {"key": "uti_result_ack", "type": "radio", "input": true, "label": "Please confirm you have reviewed and acknowledge that your Urinary Tract Infection (UTI) test result is <strong>positive</strong>.", "values": [{"label": "I acknowledge this positive result", "value": true}, {"label": "I do not acknowledge", "value": false}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "UTI result acknowledged:", "customConditional": "show = _.includes(data.flagged_aks || [], 'UTI');"}, {"key": "bv_result_ack", "type": "radio", "input": true, "label": "Please confirm you have reviewed and acknowledge that your Bacterial Vaginosis (BV) test result is <strong>positive</strong>.", "values": [{"label": "I acknowledge this positive result", "value": true}, {"label": "I do not acknowledge", "value": false}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "BV result acknowledged:", "customConditional": "show = _.includes(data.flagged_aks || [], 'BV');"}, {"key": "heading_contact_tracing_pho", "type": "content", "input": false, "label": "Content", "html": "</br><h4>Contact Tracing</h4><p>Public Health Ontario (PHO) recommends that anyone diagnosed with a sexually transmitted infection <strong>(specifically chlamydia or gonorrhea)</strong> notify sexual partners from the last 60 days (or the most recent partner if there have not been any in the last 60 days). This helps prevent re-infection and protects others’ health. If you do not feel comfortable notifying partners yourself, Public Health may assist you after obtaining their contact information.</p>", "tableView": false, "refreshOnChange": false, "customConditional": "show = _.includes(data.flagged_aks || [], 'CT') || _.includes(data.flagged_aks || [], 'GC');"}, {"key": "contact_tracing_trich_note", "type": "content", "input": false, "label": "Content", "html": "<div style=\"border:1px solid #0d6efd;background:#e7f1ff;color:#084298;border-radius:6px;padding:0.9rem;margin:0.75rem 0;\"><strong>Note about Trichomonas:</strong> Trichomonas is <em>not</em> a reportable infection to PHO. However, it is generally advisable to notify partners to reduce ongoing spread and the risk of re-infection.</div>", "tableView": false, "refreshOnChange": false, "customConditional": "show = (_.includes(data.flagged_aks || [], 'TRICH')) && (_.includes(data.flagged_aks || [], 'CT') || _.includes(data.flagged_aks || [], 'GC'));"}, {"key": "contact_tracing_ack", "type": "radio", "input": true, "label": "Please select the option that best describes your situation:", "values": [{"label": "Yes, I will notify my partners", "value": "yes"}, {"label": "No, I am not comfortable notifying partners", "value": "no"}, {"label": "I'm not sure / need help with partner notification", "value": "unsure"}, {"label": "I don't have their contact info", "value": "no_contact_info"}, {"label": "My contacts are not located within Canada", "value": "outside_canada"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Contact tracing acknowledgement:", "customConditional": "show = _.includes(data.flagged_aks || [], 'CT') || _.includes(data.flagged_aks || [], 'GC');"}, {"key": "heading_symptoms_section", "type": "content", "input": false, "label": "Content", "html": "</br><h4>Your Current Symptoms</h4><p>Please answer the following questions about your current symptoms.</p>", "tableView": false, "refreshOnChange": false}, {"key": "regular_genitourinary_symptoms", "type": "selectboxes", "input": true, "label": "Do you currently have any of the following symptoms?", "values": [{"label": "Burning or pain when urinating", "value": "dysuria"}, {"label": "Urinating more often than usual", "value": "frequency"}, {"label": "Urgent need to urinate", "value": "urgency"}, {"label": "Vaginal itching or irritation", "value": "vaginal_itch", "customConditional": "show = data.sex === 'female';"}, {"label": "Vaginal discharge that is new or different", "value": "vaginal_discharge", "customConditional": "show = data.sex === 'female';"}], "adminFlag": true, "confirm_label": "Reported regular symptoms:", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.show_all;"}, {"key": "none_of_the_above_regular_genitourinary_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_regular_genitourinary_symptoms || _.some(_.values(data.regular_genitourinary_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "regular_genitourinary_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Regular symptoms not present:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.regular_genitourinary_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "red_flag_genitourinary_symptoms", "type": "selectboxes", "input": true, "label": "Do you currently have any of the following symptoms?", "values": [{"label": "Lower abdominal pain", "value": "abdominal_pain"}, {"label": "Pelvic pain or cramping", "value": "pelvic_pain", "customConditional": "show = data.sex === 'female';"}, {"label": "Low back pain that is new", "value": "back_pain"}, {"label": "Nausea or vomiting", "value": "nausea_vomiting"}, {"label": "Fever or chills", "value": "fever_chills"}, {"label": "Feeling generally unwell", "value": "malaise"}], "adminFlag": true, "confirm_label": "Reported red-flag symptoms:", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.show_all;"}, {"key": "none_of_the_above_red_flag_genitourinary_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_red_flag_genitourinary_symptoms || _.some(_.values(data.red_flag_genitourinary_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "red_flag_genitourinary_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Red-flag symptoms not present:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.red_flag_genitourinary_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "pid_recommendation_box", "type": "content", "input": false, "label": "PID Recommendation", "html": "<div style=\"border:1px solid #dc3545;background:#f8d7da;color:#842029;border-radius:8px;padding:0.9rem;margin:0.75rem 0;line-height:1.4;\"><p><strong>Possible pelvic inflammatory disease (PID)</strong></p><p>You reported lower abdominal, pelvic, or new low-back pain. TeleTest can offer treatment with oral medication; however, <strong>oral pills alone are not enough to treat PID</strong>. PID affects the uterus, tubes, or ovaries and cannot be ruled out without an in-person pelvic exam (a doctor examines you and checks if the cervix is tender).</p><p><strong>If the cervix is tender on exam, the medical treatment changes:</strong> the recommended regimen includes an <em>injection</em> of antibiotics plus <em>14 days</em> of oral antibiotics (for example, ceftriaxone + doxycycline + metronidazole). The injection cannot be given at a pharmacy — it must be administered in a physician's office, urgent care, or emergency department.</p><p><strong>What to do:</strong> Because oral-only therapy is not sufficient for P<PERSON>, please arrange a <em>same-day</em> in-person assessment at a walk-in clinic, your family doctor, or an emergency department. If PID is suspected, clinicians usually start this treatment immediately to prevent complications such as chronic pelvic pain, infertility, or (rarely) hospitalization.</p></div>", "tableView": false, "customConditional": "show = (data.sex === 'female') && !!(data.red_flag_genitourinary_symptoms && (data.red_flag_genitourinary_symptoms.abdominal_pain || data.red_flag_genitourinary_symptoms.pelvic_pain || data.red_flag_genitourinary_symptoms.back_pain));"}, {"key": "pid_recommendation_ack", "type": "radio", "input": true, "label": "Please confirm you have read this recommendation:", "values": [{"label": "I understand and will seek same-day in-person assessment", "value": true}, {"label": "I do not understand", "value": false}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "PID recommendation acknowledged:", "customConditional": "show = (data.sex === 'female') && !!(data.red_flag_genitourinary_symptoms && (data.red_flag_genitourinary_symptoms.abdominal_pain || data.red_flag_genitourinary_symptoms.pelvic_pain || data.red_flag_genitourinary_symptoms.back_pain));"}, {"key": "heading_abstinence", "type": "content", "input": false, "label": "Content", "html": "</br><h4>Abstinence After Treatment</h4><p>It's recommended to wait <strong>7 full days after your first dose</strong> before having unprotected sex. Barrier protection (condoms, dental dams) can reduce risk in the meantime, but waiting the full period lowers the chance of <strong>re-infection</strong> and avoids passing the infection back and forth. If a partner starts treatment later than you, it's safest to wait until they have also completed 7 days of treatment before having unprotected sex.</p>", "tableView": false, "refreshOnChange": false}, {"key": "treatment_start_date", "type": "datetime", "input": true, "label": "When do you plan to start your treatment?", "format": "yyyy-MM-dd", "enableDate": true, "enableTime": false, "validate": {"required": true}, "tableView": true, "confirm_label": "Treatment start date:"}, {"key": "safe_unprotected_date", "type": "textfield", "input": true, "label": "Earliest date you can have unprotected sex:", "disabled": true, "tableView": true, "spellcheck": false, "confirm_label": "Unprotected sex date:", "calculateValue": "if (data.treatment_start_date) { try { var d = moment(data.treatment_start_date).add(8, 'days'); value = d.format('YYYY-MM-DD'); } catch(e) { value = ''; } } else { value = ''; }"}, {"key": "abstinence_ack", "type": "radio", "input": true, "label": "Do you understand that having sex too early increases the risk of re-infection, and that waiting the full period is advised?", "values": [{"label": "I understand", "value": true}, {"label": "I do not understand", "value": false}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Abstinence guidance acknowledged:"}, {"key": "heading_test_of_cure", "type": "content", "input": false, "label": "Content", "html": "</br><h4>Test of Cure</h4><p>Our STI testing is DNA-based, and can detect fragments of infection for up to 21 days after treatment. After 7 days, the infection is gone, but testing too early can still return a positive result due to these fragments. For this reason, we recommend repeat testing <strong>no earlier than 30 days after completing your treatment</strong>. This ensures the most accurate result and confirms that the infection has fully cleared.</p>", "tableView": false, "refreshOnChange": false}, {"key": "test_of_cure_ack", "type": "radio", "input": true, "label": "Do you understand that retesting should be done at least 30 days after completing treatment?", "values": [{"label": "I understand", "value": true}, {"label": "I do not understand", "value": false}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Test of cure advice acknowledged:"}, {"key": "heading_treatment_preferences", "type": "content", "input": false, "label": "Content", "html": "</br><h4>Treatment Preferences</h4><p>Please select your preferred treatment option based on your test results.</p>", "tableView": false}, {"key": "heading_ct_only", "type": "content", "input": false, "label": "Content", "html": "</br><h5><PERSON><PERSON><PERSON><PERSON> (CT) Only</h5><p><strong>Note:</strong> Doxycycline has a higher cure rate for oral and anal sites of infection (performing oral sex or receiving anal sex). Azithromycin is easier to take but may be less effective in these cases.</p>", "customConditional": "show = _.includes(data.flagged_aks || [], 'CT') && !_.includes(data.flagged_aks || [], 'GC');"}, {"key": "ct_only_rx", "type": "radio", "input": true, "label": "Which treatment do you prefer?", "values": [{"label": "Doxycycline 100 mg twice daily for 7 days", "value": "doxycycline"}, {"label": "Azithromycin 1 g (4 tablets at once)", "value": "azithromycin"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.includes(data.flagged_aks || [], 'CT') && !_.includes(data.flagged_aks || [], 'GC');"}, {"key": "heading_gc_only", "type": "content", "input": false, "label": "Content", "html": "</br><h5>Gonorrhea (GC) Only</h5><p><strong>Note:</strong> Injection treatment is associated with a higher cure rate. Injections must be given by a medical practitioner (walk-in clinic, family doctor, or ER); pharmacists cannot provide this service.</p>", "customConditional": "show = _.includes(data.flagged_aks || [], 'GC') && !_.includes(data.flagged_aks || [], 'CT');"}, {"key": "gc_only_rx", "type": "radio", "input": true, "label": "Which treatment do you prefer?", "values": [{"label": "Ceftriaxone 0.5 g injection + Azithromycin 1 g (preferred)", "value": "injection"}, {"label": "Cefixime 800 mg tablets + Azithromycin 1 g", "value": "oral"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.includes(data.flagged_aks || [], 'GC') && !_.includes(data.flagged_aks || [], 'CT');"}, {"key": "gc_injection_how_it_works", "type": "content", "input": false, "label": "Content", "html": "<div style=\"border:1px solid #0d6efd;background:#e7f1ff;color:#084298;border-radius:6px;padding:0.9rem;margin:0.75rem 0;\"><strong>How this works:</strong> We fax your prescription to your preferred pharmacy. You pick it up and take it to a local walk-in clinic, your family doctor, or an ER. A medical practitioner will give the injection there.</div>", "customConditional": "show = data.gc_only_rx === 'injection';"}, {"key": "gc_injection_ack", "type": "radio", "input": true, "label": "I understand this treatment requires picking up the medication from a pharmacy and getting the injection from a medical practitioner in person.", "values": [{"label": "I understand", "value": true}, {"label": "I do not understand", "value": false}], "validate": {"required": true}, "customConditional": "show = data.gc_only_rx === 'injection';", "tableView": true, "optionsLabelPosition": "right", "confirm_label": "GC injection pathway acknowledged:"}, {"key": "heading_ct_gc", "type": "content", "input": false, "label": "Content", "html": "</br><h5>Chlamydia (CT) + Gonorrhea (GC)</h5><p>Because both infections are present, we recommend regimens that cover both. Injection treatment is preferred due to a slightly lower failure rate, but oral therapy is available if injection is not possible.</p>", "customConditional": "show = _.includes(data.flagged_aks || [], 'CT') && _.includes(data.flagged_aks || [], 'GC');"}, {"key": "ct_gc_rx", "type": "radio", "input": true, "label": "Which treatment do you prefer?", "values": [{"label": "Ceftriaxone 0.5 g injection + Azithromycin 1 g (preferred)", "value": "injection"}, {"label": "Cefixime 800 mg tablets + Azithromycin 1 g", "value": "oral"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.includes(data.flagged_aks || [], 'CT') && _.includes(data.flagged_aks || [], 'GC');"}, {"key": "ct_gc_injection_how_it_works", "type": "content", "input": false, "label": "Content", "html": "<div style=\"border:1px solid #0d6efd;background:#e7f1ff;color:#084298;border-radius:6px;padding:0.9rem;margin:0.75rem 0;\"><strong>How this works:</strong> We fax your prescription to your preferred pharmacy. You pick it up and take it to a local walk-in clinic, your family doctor, or an ER. A medical practitioner will give the injection there.</div>", "customConditional": "show = data.ct_gc_rx === 'injection';"}, {"key": "ct_gc_injection_ack", "type": "radio", "input": true, "label": "I understand this treatment requires picking up the medication from a pharmacy and getting the injection from a medical practitioner in person.", "values": [{"label": "I understand", "value": true}, {"label": "I do not understand", "value": false}], "validate": {"required": true}, "customConditional": "show = data.ct_gc_rx === 'injection';", "tableView": true, "optionsLabelPosition": "right", "confirm_label": "CT+GC injection pathway acknowledged:"}, {"key": "heading_trich", "type": "content", "input": false, "label": "Content", "html": "</br><h5>Trichomonas (Trich)</h5><p>Standard treatment is metronidazole.</p>", "customConditional": "show = _.includes(data.flagged_aks || [], 'TRICH');"}, {"key": "trich_metro_rx", "type": "radio", "input": true, "label": "Which treatment do you prefer?", "values": [{"label": "Metronidazole 500 mg PO twice daily for 7 days", "value": "metro_po"}, {"label": "Metronidazole vaginal gel (topical)", "value": "metro_gel"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.includes(data.flagged_aks || [], 'TRICH');"}, {"key": "heading_bv", "type": "content", "input": false, "label": "Content", "html": "</br><h5>Bacterial Vaginosis (BV)</h5><p>Metronidazole is first-line. If you cannot take metronidazole or it has not worked for you, clindamycin is an alternative.</p>", "customConditional": "show = _.includes(data.flagged_aks || [], 'BV');"}, {"key": "bv_metro_evidence_info", "type": "content", "input": false, "label": "Content", "html": "<div style=\"border:1px solid #0d6efd;background:#e7f1ff;color:#084298;border-radius:6px;padding:0.9rem;margin:0.75rem 0;\"><p><strong>What does the evidence suggest?</strong></p><ol style=\"margin-left:1rem;\"> <li><strong>Highest:</strong> <em>Metronidazole 500 mg by mouth, twice daily for 7 days</em> — higher cure rates in studies (often ~80–90%).</li><li><strong>Middle:</strong> <em>Metronidazole 0.75% vaginal gel, nightly for 5 days</em> — effective for many, but generally a bit lower than the 7-day oral course and may need a repeat course.</li><li><strong>Lowest:</strong> <em>Metronidazole 2,000 mg single dose</em> — easier to take, but some study data show notably lower success (e.g., ~46% vs ~86% for the 7-day oral course).</li></ol><p>If convenience is a priority, gel or single-dose may still be reasonable; if you want the <em>best chance of cure on the first try</em>, the 7-day oral course is typically preferred.</p></div>", "tableView": false, "refreshOnChange": false, "customConditional": "show = _.includes(data.flagged_aks || [], 'BV');"}, {"key": "bv_metro_rx", "type": "radio", "input": true, "label": "Which treatment do you prefer?", "values": [{"label": "Metronidazole 2,000 mg once (single dose)", "value": "metro_2g_single"}, {"label": "Metronidazole 500 mg PO twice daily for 7 days", "value": "metro_po_bid7"}, {"label": "Metronidazole 0.75% vaginal gel, nightly for 5 days", "value": "metro_gel"}, {"label": "I don't tolerate metronidazole / it didn't work for me", "value": "no_metro"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.includes(data.flagged_aks || [], 'BV');", "confirm_label": "BV regimen selected:"}, {"key": "bv_clinda_rx", "type": "radio", "input": true, "label": "Clindamycin options", "values": [{"label": "Clindamycin 2% vaginal cream (topical)", "value": "clinda_gel"}, {"label": "Clindamycin 300 mg PO twice daily for 7 days", "value": "clinda_po"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.bv_metro_rx === 'no_metro';"}, {"key": "bv_clinda_warning", "type": "content", "input": false, "label": "Content", "html": "<div style=\"border:1px solid #dc3545;background:#f8d7da;color:#842029;border-radius:6px;padding:0.9rem;margin:0.75rem 0;\"><strong>Important:</strong> Oral clindamycin can (rarely) cause a serious bowel infection called <em>C. difficile colitis</em>. This risk applies to clindamycin taken by mouth.</div>", "customConditional": "show = data.bv_clinda_rx === 'clinda_po';"}, {"key": "bv_clinda_ack", "type": "radio", "input": true, "label": "Do you understand this risk with oral clindamycin?", "values": [{"label": "I understand", "value": true}, {"label": "I do not understand", "value": false}], "validate": {"required": true}, "customConditional": "show = data.bv_clinda_rx === 'clinda_po';", "tableView": true, "optionsLabelPosition": "right", "confirm_label": "BV clindamycin risk acknowledged:"}, {"key": "red_flag_pid_warning", "type": "content", "input": false, "label": "PID warning box", "html": "<div style=\"border:1px solid #dc3545;background:#f8d7da;color:#842029;border-radius:6px;padding:0.9rem;margin:0.75rem 0;\"><strong>Important:</strong> You reported symptoms that can suggest a more serious infection in the pelvis. TeleTest physicians can provide treatment, but same-day in-person medical care is recommended to rule out pelvic inflammatory disease (PID). PID may require different treatment, including injections and additional antibiotics.</div>", "customConditional": "show = data.sex === 'female' && _.some(_.values(data.red_flag_genitourinary_symptoms));"}, {"key": "seek_care_ack", "type": "radio", "input": true, "label": "I will seek same-day, in-person medical care if I develop new or worsening symptoms, severe pelvic/abdominal pain, fever, or feel unwell while awaiting care.", "values": [{"label": "I understand", "value": true}, {"label": "I do not understand", "value": false}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "reasons", "type": "selectboxes", "input": true, "label": "Which result(s) are you following up on?", "values": [{"label": "<PERSON><PERSON><PERSON><PERSON> (positive)", "value": "chlamydia"}, {"label": "Gonorrhea (positive)", "value": "gonorrhea"}, {"label": "Bacterial Vaginosis (BV)", "value": "bv"}, {"label": "Yeast", "value": "yeast"}, {"label": "Urinary Tract Infection (UTI)", "value": "uti"}, {"label": "Syphilis (indeterminate/past treated)", "value": "syphilis_indeterminate"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "calculateValue": "if(!value){value={}}; try{const akvs=(data.assay_kvs||{}); for (const [k,v] of Object.entries(akvs)){ if(v && v.flag===true){ const K=(k||'').toUpperCase(); if (K==='CT'){ value.chlamydia=true; } if (K==='GC'){ value.gonorrhea=true; } if (K==='BV'){ value.bv=true; } if (K==='UTI'){ value.uti=true; } if (K==='VDRL' || K==='VDRL-2'){ value.syphilis_indeterminate=true; } } } }catch(e){}", "optionsLabelPosition": "right"}, {"key": "panel_chlamydia", "type": "panel", "title": "Chlamydia", "components": [{"key": "chlamydia_rx", "type": "radio", "input": true, "label": "If treatment is provided, which chlamydia option do you prefer?", "values": [{"label": "<strong>Doxycycline</strong>: Twice daily for 7 days (preferred)", "value": "doxycycline"}, {"label": "<strong>Azithromycin</strong>: Four tablets as a single dose", "value": "azithromycin"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "chlamydia_pelvic_pain", "type": "radio", "input": true, "label": "Are you having pelvic or abdominal pain?", "values": [{"label": "No", "value": false}, {"label": "Yes", "value": true}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "chlamyd<PERSON>_partner_notify", "type": "radio", "input": true, "label": "I will notify partners from the last 60 days (or last partner if none in 60 days).", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": true}, {"key": "chlamydia_abstinence_ack", "type": "radio", "input": true, "label": "I will abstain from sex for 7 days from starting treatment and until partners have finished treatment and waited 7 days.", "values": [{"label": "I understand", "value": true}, {"label": "I do not understand", "value": false}], "validate": {"required": true}, "tableView": true}, {"key": "chlamydia_retest_plan", "type": "radio", "input": true, "label": "I plan to re-test about 3 months after treatment.", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": true}], "conditional": {"eq": true, "when": "reasons.chlamydia"}}, {"key": "panel_gonorrhea", "type": "panel", "title": "Gonorrhea", "components": [{"key": "gonorrhea_rx", "type": "radio", "input": true, "label": "Which gonorrhea treatment option do you prefer? (Injection is more effective)", "values": [{"label": "Injection: I will visit a walk-in clinic or emergency room (preferred)", "value": "injection"}, {"label": "Tablets only", "value": "tablets"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "gonorrhea_partner_notify", "type": "radio", "input": true, "label": "I will notify partners from the last 60 days.", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": true}, {"key": "gonorrhea_abstinence_ack", "type": "radio", "input": true, "label": "I will abstain until all partners are treated and 7 days have passed from the last partner starting treatment.", "values": [{"label": "I understand", "value": true}, {"label": "I do not understand", "value": false}], "validate": {"required": true}, "tableView": true}, {"key": "gonorrhea_retest_30d", "type": "radio", "input": true, "label": "I will re-test 30 days after treatment.", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": true}], "conditional": {"eq": true, "when": "reasons.gonorrhea"}}, {"key": "heading_pregnancy_status", "type": "content", "input": false, "label": "Content", "html": "</br><h4>Pregnancy Status</h4><p>Some treatments may be adjusted if you are pregnant or breastfeeding. Please let us know your status below.</p>", "tableView": false, "refreshOnChange": false}, {"key": "pregnant", "type": "radio", "input": true, "label": "Are you currently pregnant?", "values": [{"label": "No", "value": false}, {"label": "Yes", "value": true}, {"label": "Not applicable", "value": "na"}], "tableView": true, "optionsLabelPosition": "right", "validate": {"required": true}, "confirm_label": "Pregnancy status:"}, {"key": "heading_medication_safety", "type": "content", "input": false, "label": "Content", "html": "</br><h4>Medication Safety</h4><p>Please review the common side effects and confirm you understand them. Then confirm you do not have allergies to related medications and that none of the listed conditions apply to you.</p>", "tableView": false, "refreshOnChange": false}, {"key": "doxycycline_sidefx_ack", "type": "radio", "input": true, "label": "<strong>Doxycycline 100 mg BID × 7 days — side effects</strong><ul><li>Upset stomach, heartburn, nausea</li><li>Sun sensitivity (easier to burn)</li><li>Esophagus irritation if taken without water / lying down</li><li>Headache (uncommon)</li></ul>", "values": [{"label": "I understand", "value": true}, {"label": "I do not understand", "value": false}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Doxycycline side effects acknowledged:", "customConditional": "show = data.ct_only_rx === 'doxycycline';"}, {"key": "doxycycline_class_allergy", "type": "selectboxes", "input": true, "label": "Allergies or major side effects to tetracyclines?", "values": [{"label": "Doxycycline", "value": "doxy"}, {"label": "Minocycline", "value": "mino"}, {"label": "Tetracycline", "value": "tetra"}], "adminFlag": true, "tableView": true, "confirm_label": "Tetracycline-class allergy:", "optionsLabelPosition": "right", "customConditional": "show = data.ct_only_rx === 'doxycycline';"}, {"key": "none_of_the_above_doxycycline_class_allergy", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select an item."}, "validate": {"custom": "valid = !!data.none_of_the_above_doxycycline_class_allergy || _.some(_.values(data.doxycycline_class_allergy));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.ct_only_rx === 'doxycycline';"}, {"key": "doxycycline_class_allergy_not_present", "type": "textfield", "input": true, "label": "Patient indicated NO allergies to these:", "hidden": true, "disabled": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "No tetracycline-class allergy:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.doxycycline_class_allergy, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');", "customConditional": "show = data.ct_only_rx === 'doxycycline';"}, {"key": "doxycycline_contras", "type": "selectboxes", "input": true, "label": "Do any of these apply to you?", "values": [{"label": "Pregnant or possibly pregnant", "value": "pregnancy"}, {"label": "Breastfeeding", "value": "bf"}, {"label": "Severe liver disease", "value": "liver"}, {"label": "Severe esophagitis / swallowing problems", "value": "esophagus"}, {"label": "Taking isotretinoin (Accutane®)", "value": "isotretinoin"}], "adminFlag": true, "tableView": true, "confirm_label": "Doxycycline contraindications:", "optionsLabelPosition": "right", "customConditional": "show = data.ct_only_rx === 'doxycycline';"}, {"key": "none_of_the_above_doxycycline_contras", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a condition."}, "validate": {"custom": "valid = !!data.none_of_the_above_doxycycline_contras || _.some(_.values(data.doxycycline_contras));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.ct_only_rx === 'doxycycline';"}, {"key": "doxycycline_contras_not_present", "type": "textfield", "input": true, "label": "Patient indicated NONE of the above apply:", "hidden": true, "disabled": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Doxycycline contraindications not present:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.doxycycline_contras, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');", "customConditional": "show = data.ct_only_rx === 'doxycycline';"}, {"key": "azithro_sidefx_ack", "type": "radio", "input": true, "label": "<strong>Azithromycin 1 g — side effects</strong><ul><li>Nausea, diarrhea, stomach upset</li><li>Headache</li><li>Rare: heart rhythm issues in people with prolonged QT or on QT-prolonging drugs</li><li>Rare: liver irritation</li></ul>", "values": [{"label": "I understand", "value": true}, {"label": "I do not understand", "value": false}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Azithromycin side effects acknowledged:", "customConditional": "show = (data.ct_only_rx === 'azithromycin') || (data.gc_only_rx === 'injection' || data.gc_only_rx === 'oral') || (data.ct_gc_rx === 'injection' || data.ct_gc_rx === 'oral');"}, {"key": "azithro_class_allergy", "type": "selectboxes", "input": true, "label": "Allergies or major side effects to macrolides?", "values": [{"label": "Azithromycin", "value": "azith<PERSON>"}, {"label": "Erythromycin", "value": "ery"}, {"label": "Clarithromycin", "value": "clar<PERSON><PERSON>"}], "adminFlag": true, "tableView": true, "confirm_label": "Macrolide-class allergy:", "optionsLabelPosition": "right", "customConditional": "show = (data.ct_only_rx === 'azithromycin') || (data.gc_only_rx === 'injection' || data.gc_only_rx === 'oral') || (data.ct_gc_rx === 'injection' || data.ct_gc_rx === 'oral');"}, {"key": "none_of_the_above_azithro_class_allergy", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select an item."}, "validate": {"custom": "valid = !!data.none_of_the_above_azithro_class_allergy || _.some(_.values(data.azithro_class_allergy));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = (data.ct_only_rx === 'azithromycin') || (data.gc_only_rx === 'injection' || data.gc_only_rx === 'oral') || (data.ct_gc_rx === 'injection' || data.ct_gc_rx === 'oral');"}, {"key": "azithro_class_allergy_not_present", "type": "textfield", "input": true, "label": "Patient indicated NO allergies to these:", "hidden": true, "disabled": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "No macrolide-class allergy:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.azithro_class_allergy, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');", "customConditional": "show = (data.ct_only_rx === 'azithromycin') || (data.gc_only_rx === 'injection' || data.gc_only_rx === 'oral') || (data.ct_gc_rx === 'injection' || data.ct_gc_rx === 'oral');"}, {"key": "azithro_contras", "type": "selectboxes", "input": true, "label": "Do any of these apply to you?", "values": [{"label": "History of prolonged QT or serious arrhythmia", "value": "qt"}, {"label": "Taking a QT-prolonging medicine", "value": "qt_drug"}, {"label": "Severe liver disease", "value": "liver"}, {"label": "<PERSON>ast<PERSON><PERSON> gravis", "value": "mg"}], "adminFlag": true, "tableView": true, "confirm_label": "Azithromycin contraindications:", "optionsLabelPosition": "right", "customConditional": "show = (data.ct_only_rx === 'azithromycin') || (data.gc_only_rx === 'injection' || data.gc_only_rx === 'oral') || (data.ct_gc_rx === 'injection' || data.ct_gc_rx === 'oral');"}, {"key": "none_of_the_above_azithro_contras", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a condition."}, "validate": {"custom": "valid = !!data.none_of_the_above_azithro_contras || _.some(_.values(data.azithro_contras));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = (data.ct_only_rx === 'azithromycin') || (data.gc_only_rx === 'injection' || data.gc_only_rx === 'oral') || (data.ct_gc_rx === 'injection' || data.ct_gc_rx === 'oral');"}, {"key": "azithro_contras_not_present", "type": "textfield", "input": true, "label": "Patient indicated NONE of the above apply:", "hidden": true, "disabled": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Azithromycin contraindications not present:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.azithro_contras, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');", "customConditional": "show = (data.ct_only_rx === 'azithromycin') || (data.gc_only_rx === 'injection' || data.gc_only_rx === 'oral') || (data.ct_gc_rx === 'injection' || data.ct_gc_rx === 'oral');"}, {"key": "ceftriaxone_sidefx_ack", "type": "radio", "input": true, "label": "<strong>Ceftriaxone 0.5 g injection — side effects</strong><ul><li>Pain/redness at injection site</li><li>Allergic reaction (rare; more likely with severe penicillin/cephalosporin allergy)</li><li>Diarrhea or stomach upset</li></ul>", "values": [{"label": "I understand", "value": true}, {"label": "I do not understand", "value": false}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Ceftriaxone side effects acknowledged:", "customConditional": "show = (data.gc_only_rx === 'injection') || (data.ct_gc_rx === 'injection');"}, {"key": "beta_lactam_allergy", "type": "selectboxes", "input": true, "label": "Allergies or major side effects to penicillins/cephalosporins?", "values": [{"label": "Penicillin / Amoxicillin", "value": "pcn"}, {"label": "Cephalexin (Keflex®)", "value": "cephalexin"}, {"label": "Ceftriaxone", "value": "ceftriaxone"}, {"label": "Cefixime", "value": "cefixime"}], "adminFlag": true, "tableView": true, "confirm_label": "Beta-lactam allergy:", "optionsLabelPosition": "right", "customConditional": "show = (data.gc_only_rx === 'injection') || (data.ct_gc_rx === 'injection');"}, {"key": "none_of_the_above_beta_lactam_allergy", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select an item."}, "validate": {"custom": "valid = !!data.none_of_the_above_beta_lactam_allergy || _.some(_.values(data.beta_lactam_allergy));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = (data.gc_only_rx === 'injection') || (data.ct_gc_rx === 'injection');"}, {"key": "beta_lactam_allergy_not_present", "type": "textfield", "input": true, "label": "Patient indicated NO allergies to these:", "hidden": true, "disabled": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "No beta-lactam allergy:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.beta_lactam_allergy, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');", "customConditional": "show = (data.gc_only_rx === 'injection') || (data.ct_gc_rx === 'injection');"}, {"key": "ceftriaxone_contras", "type": "selectboxes", "input": true, "label": "Do any of these apply to you?", "values": [{"label": "History of anaphylaxis to penicillin or a cephalosporin", "value": "ana"}, {"label": "Severe kidney disease on dialysis", "value": "renal"}, {"label": "Bleeding disorder or on blood thinners", "value": "anticoag"}], "adminFlag": true, "tableView": true, "confirm_label": "Ceftriaxone contraindications:", "optionsLabelPosition": "right", "customConditional": "show = (data.gc_only_rx === 'injection') || (data.ct_gc_rx === 'injection');"}, {"key": "none_of_the_above_ceftriaxone_contras", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a condition."}, "validate": {"custom": "valid = !!data.none_of_the_above_ceftriaxone_contras || _.some(_.values(data.ceftriaxone_contras));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = (data.gc_only_rx === 'injection') || (data.ct_gc_rx === 'injection');"}, {"key": "ceftriaxone_contras_not_present", "type": "textfield", "input": true, "label": "Patient indicated NONE of the above apply:", "hidden": true, "disabled": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Ceftriaxone contraindications not present:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.ceftriaxone_contras, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');", "customConditional": "show = (data.gc_only_rx === 'injection') || (data.ct_gc_rx === 'injection');"}, {"key": "cefixime_sidefx_ack", "type": "radio", "input": true, "label": "<strong>Cefixime 800 mg — side effects</strong><ul><li>Diarrhea, stomach upset</li><li>Allergic reaction (rare; more likely with severe penicillin/cephalosporin allergy)</li><li>Headache (uncommon)</li></ul>", "values": [{"label": "I understand", "value": true}, {"label": "I do not understand", "value": false}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Cefixime side effects acknowledged:", "customConditional": "show = (data.gc_only_rx === 'oral') || (data.ct_gc_rx === 'oral');"}, {"key": "beta_lactam_allergy_cefixime", "type": "selectboxes", "input": true, "label": "Allergies or major side effects to penicillins/cephalosporins?", "values": [{"label": "Penicillin / Amoxicillin", "value": "pcn"}, {"label": "Cephalexin (Keflex®)", "value": "cephalexin"}, {"label": "Ceftriaxone", "value": "ceftriaxone"}, {"label": "Cefixime", "value": "cefixime"}], "adminFlag": true, "tableView": true, "confirm_label": "Beta-lactam allergy (cefixime path):", "optionsLabelPosition": "right", "customConditional": "show = (data.gc_only_rx === 'oral') || (data.ct_gc_rx === 'oral');"}, {"key": "none_of_the_above_beta_lactam_allergy_cefixime", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select an item."}, "validate": {"custom": "valid = !!data.none_of_the_above_beta_lactam_allergy_cefixime || _.some(_.values(data.beta_lactam_allergy_cefixime));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = (data.gc_only_rx === 'oral') || (data.ct_gc_rx === 'oral');"}, {"key": "beta_lactam_allergy_cefixime_not_present", "type": "textfield", "input": true, "label": "Patient indicated NO allergies to these:", "hidden": true, "disabled": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "No beta-lactam allergy (cefixime path):", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.beta_lactam_allergy_cefixime, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');", "customConditional": "show = (data.gc_only_rx === 'oral') || (data.ct_gc_rx === 'oral');"}, {"key": "cefixime_contras", "type": "selectboxes", "input": true, "label": "Do any of these apply to you?", "values": [{"label": "History of anaphylaxis to penicillin or a cephalosporin", "value": "ana"}, {"label": "Severe kidney disease on dialysis", "value": "renal"}], "adminFlag": true, "tableView": true, "confirm_label": "Cefixime contraindications:", "optionsLabelPosition": "right", "customConditional": "show = (data.gc_only_rx === 'oral') || (data.ct_gc_rx === 'oral');"}, {"key": "none_of_the_above_cefixime_contras", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a condition."}, "validate": {"custom": "valid = !!data.none_of_the_above_cefixime_contras || _.some(_.values(data.cefixime_contras));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = (data.gc_only_rx === 'oral') || (data.ct_gc_rx === 'oral');"}, {"key": "cefixime_contras_not_present", "type": "textfield", "input": true, "label": "Patient indicated NONE of the above apply:", "hidden": true, "disabled": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Cefixime contraindications not present:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.cefixime_contras, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');", "customConditional": "show = (data.gc_only_rx === 'oral') || (data.ct_gc_rx === 'oral');"}, {"key": "metronidazole_oral_sidefx_ack", "type": "radio", "input": true, "label": "<strong>Metronidazole (oral) — side effects</strong><ul><li>Nausea, metallic taste, stomach upset</li><li>Headache</li><li>Avoid alcohol during treatment and for at least 24 hours after the last dose</li><li>Rare: tingling/numbness in hands/feet</li></ul>", "values": [{"label": "I understand", "value": true}, {"label": "I do not understand", "value": false}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Metronidazole oral side effects acknowledged:", "customConditional": "show = (data.trich_metro_rx === 'metro_po') || (data.bv_metro_rx === 'metro_po_bid7' || data.bv_metro_rx === 'metro_2g_single');"}, {"key": "nitroimidazole_allergy", "type": "selectboxes", "input": true, "label": "Allergies or major side effects to nitroimidazoles?", "values": [{"label": "Metronidazole", "value": "metro"}, {"label": "Tinidazole", "value": "tini"}], "adminFlag": true, "tableView": true, "confirm_label": "Nitroimidazole-class allergy:", "optionsLabelPosition": "right", "customConditional": "show = (data.trich_metro_rx === 'metro_po') || (data.bv_metro_rx === 'metro_po_bid7' || data.bv_metro_rx === 'metro_2g_single');"}, {"key": "none_of_the_above_nitroimidazole_allergy", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select an item."}, "validate": {"custom": "valid = !!data.none_of_the_above_nitroimidazole_allergy || _.some(_.values(data.nitroimidazole_allergy));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = (data.trich_metro_rx === 'metro_po') || (data.bv_metro_rx === 'metro_po_bid7' || data.bv_metro_rx === 'metro_2g_single');"}, {"key": "nitroimidazole_allergy_not_present", "type": "textfield", "input": true, "label": "Patient indicated NO allergies to these:", "hidden": true, "disabled": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "No nitroimidazole-class allergy:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.nitroimidazole_allergy, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');", "customConditional": "show = (data.trich_metro_rx === 'metro_po') || (data.bv_metro_rx === 'metro_po_bid7' || data.bv_metro_rx === 'metro_2g_single');"}, {"key": "metronidazole_oral_contras", "type": "selectboxes", "input": true, "label": "Do any of these apply to you?", "values": [{"label": "Planning to drink alcohol during treatment or within 24 hours after last dose", "value": "alcohol"}, {"label": "Severe liver disease", "value": "liver"}, {"label": "Took disulfiram (Antabuse®) in the last 2 weeks", "value": "disulfiram"}, {"label": "On warfarin (bleeding-thinner) — needs monitoring", "value": "warfarin"}, {"label": "Pregnant in first trimester (discuss with clinician)", "value": "pregnancy"}], "adminFlag": true, "tableView": true, "confirm_label": "Metronidazole oral contraindications:", "optionsLabelPosition": "right", "customConditional": "show = (data.trich_metro_rx === 'metro_po') || (data.bv_metro_rx === 'metro_po_bid7' || data.bv_metro_rx === 'metro_2g_single');"}, {"key": "none_of_the_above_metronidazole_oral_contras", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a condition."}, "validate": {"custom": "valid = !!data.none_of_the_above_metronidazole_oral_contras || _.some(_.values(data.metronidazole_oral_contras));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = (data.trich_metro_rx === 'metro_po') || (data.bv_metro_rx === 'metro_po_bid7' || data.bv_metro_rx === 'metro_2g_single');"}, {"key": "metronidazole_oral_contras_not_present", "type": "textfield", "input": true, "label": "Patient indicated NONE of the above apply:", "hidden": true, "disabled": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Metronidazole oral contraindications not present:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.metronidazole_oral_contras, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');", "customConditional": "show = (data.trich_metro_rx === 'metro_po') || (data.bv_metro_rx === 'metro_po_bid7' || data.bv_metro_rx === 'metro_2g_single');"}, {"key": "metronidazole_gel_sidefx_ack", "type": "radio", "input": true, "label": "<strong>Metronidazole 0.75% vaginal gel — side effects</strong><ul><li>Mild vaginal irritation or discharge changes</li><li>Metallic taste / mild stomach upset (uncommon)</li></ul>", "values": [{"label": "I understand", "value": true}, {"label": "I do not understand", "value": false}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Metronidazole gel side effects acknowledged:", "customConditional": "show = (data.trich_metro_rx === 'metro_gel') || (data.bv_metro_rx === 'metro_gel');"}, {"key": "metronidazole_gel_contras", "type": "selectboxes", "input": true, "label": "Do any of these apply to you?", "values": [{"label": "Allergy to metronidazole", "value": "allergy"}, {"label": "Severe vaginal irritation with topical products", "value": "irritation"}], "adminFlag": true, "tableView": true, "confirm_label": "Metronidazole gel contraindications:", "optionsLabelPosition": "right", "customConditional": "show = (data.trich_metro_rx === 'metro_gel') || (data.bv_metro_rx === 'metro_gel');"}, {"key": "none_of_the_above_metronidazole_gel_contras", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a condition."}, "validate": {"custom": "valid = !!data.none_of_the_above_metronidazole_gel_contras || _.some(_.values(data.metronidazole_gel_contras));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = (data.trich_metro_rx === 'metro_gel') || (data.bv_metro_rx === 'metro_gel');"}, {"key": "metronidazole_gel_contras_not_present", "type": "textfield", "input": true, "label": "Patient indicated NONE of the above apply:", "hidden": true, "disabled": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Metronidazole gel contraindications not present:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.metronidazole_gel_contras, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');", "customConditional": "show = (data.trich_metro_rx === 'metro_gel') || (data.bv_metro_rx === 'metro_gel');"}, {"key": "clinda_sidefx_ack", "type": "radio", "input": true, "label": "<strong>Clindamycin (BV) — side effects</strong><ul><li>Diarrhea, stomach upset</li><li>Vaginal irritation (topical)</li><li><em>Oral only:</em> rare but serious C. difficile colitis</li></ul>", "values": [{"label": "I understand", "value": true}, {"label": "I do not understand", "value": false}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Clindamycin side effects acknowledged:", "customConditional": "show = (data.bv_clinda_rx === 'clinda_gel' || data.bv_clinda_rx === 'clinda_po');"}, {"key": "lincosamide_allergy", "type": "selectboxes", "input": true, "label": "Allergies or major side effects to lincosamides?", "values": [{"label": "Clindamycin", "value": "clinda"}, {"label": "Lincomycin", "value": "linco"}], "adminFlag": true, "tableView": true, "confirm_label": "Lincosamide-class allergy:", "optionsLabelPosition": "right", "customConditional": "show = (data.bv_clinda_rx === 'clinda_gel' || data.bv_clinda_rx === 'clinda_po');"}, {"key": "none_of_the_above_lincosamide_allergy", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select an item."}, "validate": {"custom": "valid = !!data.none_of_the_above_lincosamide_allergy || _.some(_.values(data.lincosamide_allergy));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = (data.bv_clinda_rx === 'clinda_gel' || data.bv_clinda_rx === 'clinda_po');"}, {"key": "lincosamide_allergy_not_present", "type": "textfield", "input": true, "label": "Patient indicated NO allergies to these:", "hidden": true, "disabled": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "No lincosamide-class allergy:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.lincosamide_allergy, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');", "customConditional": "show = (data.bv_clinda_rx === 'clinda_gel' || data.bv_clinda_rx === 'clinda_po');"}, {"key": "clinda_contras", "type": "selectboxes", "input": true, "label": "Do any of these apply to you?", "values": [{"label": "History of C. difficile colitis ", "value": "cdiff"}, {"label": "Inflammatory bowel disease flare risk (oral route)", "value": "ibd"}, {"label": "Severe reaction to clindamycin in the past", "value": "severe_rxn"}], "adminFlag": true, "tableView": true, "confirm_label": "Clindamycin contraindications:", "optionsLabelPosition": "right", "customConditional": "show = (data.bv_clinda_rx === 'clinda_gel' || data.bv_clinda_rx === 'clinda_po');"}, {"key": "none_of_the_above_clinda_contras", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a condition."}, "validate": {"custom": "valid = !!data.none_of_the_above_clinda_contras || _.some(_.values(data.clinda_contras));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = (data.bv_clinda_rx === 'clinda_gel' || data.bv_clinda_rx === 'clinda_po');"}, {"key": "clinda_contras_not_present", "type": "textfield", "input": true, "label": "Patient indicated NONE of the above apply:", "hidden": true, "disabled": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Clindamycin contraindications not present:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.clinda_contras, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');", "customConditional": "show = (data.bv_clinda_rx === 'clinda_gel' || data.bv_clinda_rx === 'clinda_po');"}, {"key": "any_other_questions", "type": "textarea", "input": true, "label": "Any other questions or details for your doctor?", "tableView": true, "autoExpand": true}, {"key": "rxts", "type": "textfield", "input": true, "label": "Rx Templates:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = _.concat((data.gonorrhea_rx=='injection'?['azithromycin-1000mg-od']:(data.gonorrhea_rx=='tablets'?['azithromycin-1000mg-od','auro-cefixime-400mg']:[])),(data.chlamydia_pelvic_pain===true?[]:(data.chlamydia_rx=='azithromycin'?['azithromycin-1000mg-od']:(data.chlamydia_rx=='doxycycline'?['doxycycline-tablets-100mg-bid-7']:[]))),((data.reasons&&data.reasons.bv)?(data.bv_treatment=='tablet'?['auro-metronidazole-500mg-bid']:(data.bv_treatment=='gel'?['metronidazole-gel-075p-5-g-one-full-applicator']:(data.bv_treatment=='single'?['auro-metronidazole-500mg-2g']:(data.bv_treatment=='clindagel'?['clindamycin-2p-cream-intravaginal-one-full-applicator-5-g']:[])))):[]),((data.reasons&&data.reasons.yeast&&(data.pregnant===false||data.pregnant==='na'))?(data.yeast_rx_choice=='fluconazole'?['bio-fluconazole-150mg-single-dose']:[]):[]),((data.reasons&&data.reasons.uti&&data.uti_symptomatic===true&&data.uti_antibiotics_preference===true)?['septra-ds-tablets-160mg800mg-3-days']:[]));", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = 'fuf';"}]}