{"components": [{"key": "example_assay_kvs", "type": "textfield", "input": true, "label": "Example <PERSON><PERSON>:", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": [{"flag": true, "test": "CT", "assay": "CT", "units": "", "result": "POSITIVE", "reference_range": ""}, {"flag": false, "test": "GC", "assay": "GC", "result": "NEGATIVE", "reference_range": "", "units": ""}, {"flag": false, "test": "UTI", "assay": "UTI", "result": "", "reference_range": "", "units": ""}, {"assay": "HIV", "flag": false, "test": "HIV", "result": "non-reactive", "reference_range": "", "units": ""}, {"assay": "VDRL", "flag": false, "test": "VDRL", "result": "non-reactive", "reference_range": "", "units": ""}]}, {"key": "assay_kvs", "type": "textfield", "input": true, "label": "Computed Assay <PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "try{const src=(data.datagrid_assay_kvs||data.example_assay_kvs); if(!src){ value={}; } else if(_.isArray(src)){ value=_.reduce(src,(acc,x)=>{ const k=_.toUpper(x.assay||x.test||x.key||''); if(k){ acc[k]=x; } return acc; },{});} else { value=src; }}catch(e){ value={}; }"}, {"key": "heading", "type": "content", "input": false, "label": "Content", "html": "<h2 style=\"text-align:center;\"><strong>STD Follow-Up Form</strong></h2><p>Please complete the questions below so we can provide safe, effective next steps without a live chat.</p>", "tableView": false}, {"key": "seek_care_ack", "type": "radio", "input": true, "label": "I will seek same-day, in-person medical care if I develop new or worsening symptoms, severe pelvic/abdominal pain, fever, or feel unwell while awaiting care.", "values": [{"label": "I understand", "value": true}, {"label": "I do not understand", "value": false}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "flagged_aks", "type": "textfield", "input": true, "label": "Flagged <PERSON><PERSON>s", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "defaultValue": [], "calculateValue": "var items=_.isArray(data.assay_kvs)?data.assay_kvs:_.map(data.assay_kvs,(v,k)=>_.assign({key:k},v)); value = _.chain(items).filter({flag:true}).map(x=>_.toUpper(x.assay||x.test||x.key)).uniq().value();"}, {"key": "panel_chlamydia", "type": "panel", "title": "Chlamydia", "customConditional": "show = _.includes(data.flagged_aks,'CT');", "components": [{"key": "chlamydia_rx", "type": "radio", "input": true, "label": "If treatment is provided, which chlamydia option do you prefer?", "values": [{"label": "<strong>Doxycycline</strong>: Twice daily for 7 days (preferred)", "value": "doxycycline"}, {"label": "<strong>Azithromycin</strong>: Four tablets as a single dose", "value": "azithromycin"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "chlamydia_pelvic_pain", "type": "radio", "input": true, "label": "Are you having pelvic or abdominal pain?", "values": [{"label": "No", "value": false}, {"label": "Yes", "value": true}], "validate": {"required": true}, "optionsLabelPosition": "right", "tableView": true}, {"key": "chlamyd<PERSON>_partner_notify", "type": "radio", "input": true, "label": "I will notify partners from the last 60 days (or last partner if none in 60 days).", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": true}, {"key": "chlamydia_abstinence_ack", "type": "radio", "input": true, "label": "I will abstain from sex for 7 days from starting treatment and until partners have finished treatment and waited 7 days.", "values": [{"label": "I understand", "value": true}, {"label": "I do not understand", "value": false}], "validate": {"required": true}, "tableView": true}, {"key": "chlamydia_retest_plan", "type": "radio", "input": true, "label": "I plan to re-test about 3 months after treatment.", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": true}]}, {"key": "panel_gonorrhea", "type": "panel", "title": "Gonorrhea", "customConditional": "show = _.includes(data.flagged_aks,'GC');", "components": [{"key": "gonorrhea_rx", "type": "radio", "input": true, "label": "Which gonorrhea treatment option do you prefer? (Injection is more effective)", "values": [{"label": "Injection: I will visit a walk-in clinic or emergency room (preferred)", "value": "injection"}, {"label": "Tablets only", "value": "tablets"}], "validate": {"required": true}, "optionsLabelPosition": "right", "tableView": true}, {"key": "gonorrhea_partner_notify", "type": "radio", "input": true, "label": "I will notify partners from the last 60 days.", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": true}, {"key": "gonorrhea_abstinence_ack", "type": "radio", "input": true, "label": "I will abstain until all partners are treated and 7 days have passed from the last partner starting treatment.", "values": [{"label": "I understand", "value": true}, {"label": "I do not understand", "value": false}], "validate": {"required": true}, "tableView": true}, {"key": "gonorrhea_retest_30d", "type": "radio", "input": true, "label": "I will re-test 30 days after treatment.", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": true}]}, {"key": "panel_bv", "type": "panel", "title": "Bacterial Vaginosis (BV)", "customConditional": "show = _.includes(data.flagged_aks,'BV');", "components": [{"key": "bv_symptoms_ongoing", "type": "radio", "input": true, "label": "Are you still having BV symptoms (discharge/odour/itch)?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": true}, {"key": "bv_treatment", "type": "radio", "input": true, "label": "Preferred treatment", "values": [{"label": "Metronidazole 500mg tablets, twice daily for 7 days", "value": "tablet"}, {"label": "Metronidazole 0.75% gel, once nightly for 5 days", "value": "gel"}, {"label": "Metronidazole 2g single oral dose (lower cure rate)", "value": "single"}, {"label": "Clindamycin 2% vaginal cream, once daily at bedtime for 7 days", "value": "clind<PERSON>l"}], "validate": {"required": true}, "tableView": true}, {"key": "bv_no_alcohol", "type": "radio", "input": true, "label": "If metronidazole tablets/single-dose selected: I will avoid alcohol while taking metronidazole.", "customConditional": "show = data.bv_treatment == 'tablet';", "values": [{"label": "I understand", "value": true}, {"label": "I do not understand", "value": false}], "validate": {"required": true}, "tableView": true}]}, {"key": "panel_yeast", "type": "panel", "title": "Yeast", "customConditional": "show = _.includes(data.flagged_aks,'YEAST');", "components": [{"key": "yeast_otc_tried", "type": "radio", "input": true, "label": "Have you already tried an over-the-counter treatment?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": true}, {"key": "yeast_rx_choice", "type": "radio", "input": true, "label": "Preferred treatment", "values": [{"label": "Fluconazole 150mg, single oral dose", "value": "fluconazole"}], "validate": {"required": true}, "tableView": true}]}, {"key": "panel_uti", "type": "panel", "title": "Urinary Tract Infection (UTI)", "customConditional": "show = _.includes(data.flagged_aks,'UTI');", "components": [{"key": "uti_symptomatic", "type": "radio", "input": true, "label": "Are you currently having urinary symptoms (painful urination, frequency, urgency)?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": true}, {"key": "uti_antibiotics_preference", "type": "radio", "input": true, "label": "If symptomatic: I would like antibiotic treatment.", "customConditional": "show = data.uti_symptomatic == true;", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": true}]}, {"key": "panel_syphilis", "type": "panel", "title": "Syphilis (Indeterminate/Past Treated)", "customConditional": "show = _.intersection(data.flagged_aks,['VDRL','VDRL-2']).length>0;", "components": [{"key": "syphilis_recent_exposure", "type": "radio", "input": true, "label": "Have you had a recent exposure that concerns you?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "tableView": true}, {"key": "syphilis_repeat_12w_ack", "type": "radio", "input": true, "label": "I understand repeat testing ~12 weeks from last exposure may be advised.", "values": [{"label": "I understand", "value": true}, {"label": "I do not understand", "value": false}], "tableView": true}]}, {"key": "pregnant", "type": "radio", "input": true, "label": "Are you currently pregnant?", "values": [{"label": "No", "value": false}, {"label": "Yes", "value": true}, {"label": "Not applicable", "value": "na"}], "tableView": true}, {"key": "any_other_questions", "type": "textarea", "input": true, "label": "Any other questions or details for your doctor?", "autoExpand": true, "tableView": true}, {"key": "rxts", "type": "textfield", "input": true, "label": "Rx Templates:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "var has=c=>_.isArray(c)?_.intersection(data.flagged_aks, _.map(c, _.toUpper)).length>0:_.includes(data.flagged_aks, _.toUpper(c)); value = _.concat( has('GC') ? (data.gonorrhea_rx=='injection'?['azithromycin-1000mg-od']:(data.gonorrhea_rx=='tablets'?['azithromycin-1000mg-od','auro-cefixime-400mg']:[])) : [], (has('CT') && data.chlamydia_pelvic_pain!==true) ? (data.chlamydia_rx=='azithromycin'?['azithromycin-1000mg-od']:(data.chlamydia_rx=='doxycycline'?['doxycycline-tablets-100mg-bid-7']:[])) : [], has('BV') ? (data.bv_treatment=='tablet'?['auro-metronidazole-500mg-bid']:(data.bv_treatment=='gel'?['metronidazole-gel-075p-5-g-one-full-applicator']:(data.bv_treatment=='single'?['auro-metronidazole-500mg-2g']:(data.bv_treatment=='clindagel'?['clindamycin-2p-cream-intravaginal-one-full-applicator-5-g']:[])))) : [], has('YEAST') && (data.pregnant===false || data.pregnant==='na') ? (data.yeast_rx_choice=='fluconazole'?['bio-fluconazole-150mg-single-dose']:[]) : [], has('UTI') && data.uti_symptomatic===true && data.uti_antibiotics_preference===true ? ['septra-ds-tablets-160mg800mg-3-days'] : [] );", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = 'fuf';"}]}