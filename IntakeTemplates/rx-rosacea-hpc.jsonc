{"components": [{"key": "heading_intro_title", "html": "<h1><strong>Rosacea Intake - Your Current Concerns</strong></h1><p>To expedite your care, please answer a few quick questions about your rosacea. Your answers help us confirm your rosacea subtype and recommend the most effective treatment.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_reason_consult", "html": "</br><h4>Why You're Reaching Out About Your Rosacea Today</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "consult_reason", "type": "selectboxes", "input": true, "label": "I would like help with:", "inline": false, "values": [{"label": "Renewal of a previous rosacea prescription", "value": "renewal"}, {"label": "Treatment for my current flare", "value": "current_flare"}, {"label": "Preventive maintenance to avoid future flares", "value": "maintenance"}, {"label": "Managing side-effects of my current treatment", "value": "side_effects"}, {"label": "Change in treatment options (e.g., switching medications)", "value": "change_options"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Phase that summarizes the question:", "optionsLabelPosition": "right"}, {"key": "consult_reason_other", "type": "textarea", "input": true, "label": "Please tell us more:", "tableView": true, "autoExpand": false, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.consult_reason && (data.consult_reason.other === true || data.consult_reason.none === true);"}, {"key": "rosacea_subtype", "type": "select", "input": true, "label": "What were you told about your rosacea?", "widget": "html5", "data": {"values": [{"label": "Rosacea with mainly redness (often with small visible veins)", "value": "etr"}, {"label": "Rosacea with acne-like bumps or whiteheads", "value": "papulopus<PERSON><PERSON>"}, {"label": "Rosacea with thicker skin (for example, on the nose)", "value": "phymatous"}, {"label": "Rosacea that affects the eyes (dry, irritated, red eyes)", "value": "ocular"}, {"label": "I was told I have rosacea, but not the type", "value": "unsure"}, {"label": "Other skin condition (not rosacea)", "value": "other"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.skin_diagnosis_known === 'yes';"}, {"key": "rosacea_subtype_other", "type": "textarea", "input": true, "label": "Please tell us the name of the condition you were told you have:", "tableView": true, "autoExpand": false, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.rosacea_subtype === 'other';"}, {"key": "diagnosing_provider", "type": "radio", "input": true, "label": "Who told you this diagnosis?", "inline": false, "values": [{"label": "Family doctor", "value": "family_doctor"}, {"label": "Dermatologist", "value": "dermatologist"}, {"label": "Nurse practitioner", "value": "nurse_practitioner"}, {"label": "<PERSON><PERSON><PERSON>", "value": "naturopath"}, {"label": "I figured it out myself", "value": "self"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.skin_diagnosis_known === 'yes';", "optionsLabelPosition": "right"}, {"key": "diagnosing_provider_other", "type": "textarea", "input": true, "label": "Please tell us who diagnosed you:", "tableView": true, "autoExpand": false, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.diagnosing_provider === 'other';"}, {"key": "heading_rosacea_flare_course", "html": "</br><h4>Current <PERSON><PERSON></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "rosacea_flare_start_time", "type": "select", "input": true, "label": "When did your current flare begin?", "widget": "html5", "data": {"values": [{"label": "Less than 1 week ago", "value": "duration_days_lt7"}, {"label": "1-2 weeks ago", "value": "duration_weeks_1_2"}, {"label": "2-4 weeks ago", "value": "duration_weeks_2_4"}, {"label": "1-3 months ago", "value": "duration_months_1_3"}, {"label": "3-6 months ago", "value": "duration_months_3_6"}, {"label": "More than 6 months ago", "value": "duration_months_gt6"}, {"label": "I'm not sure", "value": "duration_unknown"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:"}, {"key": "rosacea_first_or_repeat", "type": "radio", "input": true, "label": "Is this your first rosacea flare, or have you had them before?", "inline": false, "values": [{"label": "First flare", "value": "first"}, {"label": "I've had flares before", "value": "repeat"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = !!data.rosacea_flare_start_time;", "optionsLabelPosition": "right"}, {"key": "rosacea_flare_onset_speed", "type": "radio", "input": true, "label": "Did this flare come on suddenly or build up gradually?", "inline": false, "values": [{"label": "<PERSON><PERSON>", "value": "sudden"}, {"label": "Gradual", "value": "gradual"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = !!data.rosacea_first_or_repeat;", "optionsLabelPosition": "right"}, {"key": "rosacea_course_since_start", "type": "select", "input": true, "label": "Since it started, has your rosacea mostly:", "widget": "html5", "data": {"values": [{"label": "Been getting better", "value": "better"}, {"label": "Been getting worse", "value": "worse"}, {"label": "Gone up and down", "value": "fluctuating"}, {"label": "Stayed about the same", "value": "same"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = !!data.rosacea_flare_onset_speed;"}, {"key": "rosacea_flare_frequency_year", "type": "select", "input": true, "label": "In a typical year, how many flares do you get?", "widget": "html5", "data": {"values": [{"label": "Less than 1 flare", "value": "freq_year_lt1"}, {"label": "1-2 flares", "value": "freq_year_1_2"}, {"label": "3-6 flares", "value": "freq_year_3_6"}, {"label": "Monthly", "value": "freq_monthly"}, {"label": "Almost all the time", "value": "freq_constant"}, {"label": "I don't know", "value": "freq_unknown"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = !!data.rosacea_course_since_start;"}, {"key": "rosacea_flare_duration_typical", "type": "select", "input": true, "label": "When you have a flare, how long does it usually last?", "widget": "html5", "data": {"values": [{"label": "A few days", "value": "few_days"}, {"label": "About 1 week", "value": "week_1"}, {"label": "2-4 weeks", "value": "weeks_2_4"}, {"label": "1-3 months", "value": "months_1_3"}, {"label": "More than 3 months", "value": "months_gt3"}, {"label": "I'm not sure", "value": "unknown"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.rosacea_first_or_repeat === 'repeat' && !!data.rosacea_flare_frequency_year;"}, {"key": "flare_resolution_pattern", "type": "radio", "input": true, "label": "How do your rosacea flares usually get better?", "inline": false, "values": [{"label": "They only improve with prescription treatment", "value": "requires_prescription"}, {"label": "They improve faster with prescription treatment", "value": "faster_with_prescription"}, {"label": "They sometimes improve on their own", "value": "sometimes_spontaneous"}, {"label": "They usually improve on their own, even without treatment", "value": "usually_spontaneous"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.rosacea_flare_duration_typical && data.rosacea_flare_duration_typical !== 'unknown';", "optionsLabelPosition": "right"}, {"key": "otc_effectiveness", "type": "radio", "input": true, "label": "Do over-the-counter products (non-prescription creams or washes) help your rosacea?", "inline": false, "values": [{"label": "Yes - they usually help", "value": "yes_often"}, {"label": "Sometimes - depends on the product", "value": "sometimes"}, {"label": "No - they don't seem to help", "value": "no_effect"}, {"label": "I haven't really tried", "value": "not_tried"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.flare_resolution_pattern && data.flare_resolution_pattern !== 'unsure';", "optionsLabelPosition": "right"}, {"key": "heading_otc_rosacea", "html": "</br><h4>Non-Prescription Skin Care for Rosacea</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "otc_effectiveness", "type": "radio", "input": true, "label": "Do non-prescription products (gentle cleansers, moisturizers, or sunscreens) help your rosacea?", "inline": false, "values": [{"label": "Yes - they usually help", "value": "yes_often"}, {"label": "Sometimes - depends on the product", "value": "sometimes"}, {"label": "No - they don't seem to help", "value": "no_effect"}, {"label": "I haven't really tried them", "value": "not_tried"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "optionsLabelPosition": "right"}, {"key": "otc_products_used", "type": "selectboxes", "input": true, "label": "Which non-prescription products are you using now (or used recently)?", "inline": false, "values": [{"label": "Gentle, non-foaming cleanser", "value": "gentle_cleanser"}, {"label": "Bland, fragrance-free moisturizer", "value": "bland_moisturizer"}, {"label": "Mineral sunscreen SPF 30+ (zinc/titanium)", "value": "mineral_sunscreen"}, {"label": "Green-tinted moisturizer or primer (to reduce redness)", "value": "green_tint"}, {"label": "Niacinamide serum/cream", "value": "niacinamide"}, {"label": "Azelaic acid 10% (OTC)", "value": "azelaic_otc"}, {"label": "Sulfur wash/soap", "value": "sulfur"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.otc_effectiveness && data.otc_effectiveness !== 'not_tried';", "optionsLabelPosition": "right"}, {"key": "otc_products_used_other", "type": "textarea", "input": true, "label": "Please list the other product(s):", "tableView": true, "autoExpand": false, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.otc_products_used && data.otc_products_used.other === true;"}, {"key": "sunscreen_use_frequency", "type": "radio", "input": true, "label": "How often do you apply sunscreen to your face?", "inline": false, "values": [{"label": "Daily (most days of the week)", "value": "daily"}, {"label": "Sometimes", "value": "sometimes"}, {"label": "Rarely / never", "value": "rarely_never"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.otc_products_used && (data.otc_products_used.mineral_sunscreen || data.otc_products_used.green_tint || data.otc_products_used.bland_moisturizer || data.otc_products_used.gentle_cleanser);", "optionsLabelPosition": "right"}, {"key": "sunscreen_type", "type": "select", "input": true, "label": "If you use sunscreen, which type is it mostly?", "widget": "html5", "data": {"values": [{"label": "Mineral (zinc oxide and/or titanium dioxide)", "value": "mineral"}, {"label": "Chemical (e.g., oxybenzone, avobenzone, etc.)", "value": "chemical"}, {"label": "I'm not sure", "value": "unsure"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.sunscreen_use_frequency && data.sunscreen_use_frequency !== 'rarely_never';"}, {"key": "moisturizer_use_frequency", "type": "radio", "input": true, "label": "How often do you use a moisturizer on your face?", "inline": false, "values": [{"label": "Twice daily", "value": "twice_daily"}, {"label": "Once daily", "value": "once_daily"}, {"label": "Occasionally", "value": "sometimes"}, {"label": "Rarely / never", "value": "rarely_never"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.otc_products_used && (data.otc_products_used.bland_moisturizer || data.otc_products_used.gentle_cleanser || data.otc_products_used.azelaic_otc || data.otc_products_used.niacinamide);", "optionsLabelPosition": "right"}, {"key": "heading_rosacea_severity_distribution", "html": "</br><h4>Severity &amp; Areas</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "redness_severity", "type": "select", "input": true, "label": "How noticeable is the facial redness right now?", "widget": "html5", "data": {"values": [{"label": "None / not noticeable", "value": "none"}, {"label": "Mild", "value": "mild"}, {"label": "Moderate", "value": "moderate"}, {"label": "Severe", "value": "severe"}]}, "validate": {"required": true}, "adminFlag": true, "tableView": true, "confirm_label": "Phase that summarizes the question:"}, {"key": "rosacea_features_present", "type": "selectboxes", "input": true, "label": "Which of these do you notice right now? (Select all that apply)", "inline": false, "values": [{"label": "Facial redness that doesn't fully go away", "value": "persistent_redness"}, {"label": "Sudden flushing episodes (redness that comes and goes)", "value": "flushing_episodes"}, {"label": "Small visible veins on the cheeks or nose", "value": "visible_veins"}, {"label": "Red bumps without pus", "value": "papules"}, {"label": "Bumps filled with pus", "value": "pustules"}, {"label": "Burning or stinging feeling on the skin", "value": "burning_stinging"}, {"label": "Skin feels hot or warm to the touch", "value": "skin_warmth"}, {"label": "Swelling or puffiness (especially around the eyes)", "value": "swelling"}, {"label": "Thicker skin (often on the nose)", "value": "thicker_skin"}, {"label": "Dry, rough, or scaly patches", "value": "dry_scaly"}, {"label": "Eye problems (dry, gritty, red, or watery eyes)", "value": "ocular_symptoms"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "adminFlag": true, "tableView": true, "confirm_label": "Current rosacea features:", "optionsLabelPosition": "right"}, {"key": "bump_count_estimate", "type": "number", "input": true, "label": "About how many acne-like bumps do you see on your face today (all areas combined)?", "suffix": "bumps", "validate": {"min": 0, "required": true}, "adminFlag": true, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = !!data.rosacea_features_present && (data.rosacea_features_present.papules || data.rosacea_features_present.pustules);"}, {"key": "rosacea_features_other", "type": "textarea", "input": true, "label": "Please describe any other skin changes:", "tableView": true, "autoExpand": false, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = !!data.rosacea_features_present && data.rosacea_features_present.other === true;"}, {"key": "flushing_frequency", "type": "radio", "input": true, "label": "How often do you experience flushing episodes (sudden redness that comes and goes)?", "inline": false, "values": [{"label": "Multiple times daily", "value": "multiple_daily"}, {"label": "Daily", "value": "daily"}, {"label": "Several times per week", "value": "several_weekly"}, {"label": "Weekly", "value": "weekly"}, {"label": "Monthly or less", "value": "monthly_less"}, {"label": "I don't experience flushing episodes", "value": "no_flushing"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Flushing frequency:", "customConditional": "show = !!data.rosacea_features_present && data.rosacea_features_present.flushing_episodes;", "optionsLabelPosition": "right"}, {"key": "flushing_duration", "type": "radio", "input": true, "label": "When you do flush, how long does the redness typically last?", "inline": false, "values": [{"label": "A few minutes", "value": "minutes"}, {"label": "30 minutes to 1 hour", "value": "hour"}, {"label": "Several hours", "value": "hours"}, {"label": "All day", "value": "all_day"}, {"label": "It varies a lot", "value": "varies"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Flushing duration:", "customConditional": "show = data.flushing_frequency && data.flushing_frequency !== 'no_flushing';", "optionsLabelPosition": "right"}, {"key": "flushing_triggers", "type": "selectboxes", "input": true, "label": "What typically triggers your flushing episodes? (Select all that apply)", "inline": false, "values": [{"label": "Hot drinks or food", "value": "hot_drinks_food"}, {"label": "Spicy food", "value": "spicy_food"}, {"label": "Alcohol", "value": "alcohol"}, {"label": "Sun exposure", "value": "sun_exposure"}, {"label": "Hot weather or heat", "value": "hot_weather"}, {"label": "Exercise or physical activity", "value": "exercise"}, {"label": "Stress or strong emotions", "value": "stress_emotions"}, {"label": "Certain skincare products", "value": "skincare_products"}, {"label": "Wind or cold weather", "value": "wind_cold"}, {"label": "Hormonal changes (menstrual cycle, menopause)", "value": "hormonal_changes"}, {"label": "No clear triggers identified", "value": "no_triggers"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Flushing triggers:", "customConditional": "show = data.flushing_frequency && data.flushing_frequency !== 'no_flushing';", "optionsLabelPosition": "right"}, {"key": "symptom_severity_assessment", "type": "radio", "input": true, "label": "Overall, how would you rate the severity of your current rosacea symptoms?", "inline": false, "values": [{"label": "Mild - barely noticeable, doesn't bother me much", "value": "mild"}, {"label": "Moderate - noticeable, somewhat bothersome", "value": "moderate"}, {"label": "Severe - very noticeable, significantly impacts my daily life", "value": "severe"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Symptom severity:", "customConditional": "show = !!data.rosacea_features_present;", "optionsLabelPosition": "right"}, {"key": "predominant_subtype_assessment", "type": "radio", "input": true, "label": "Which best describes your main rosacea concern right now?", "inline": false, "values": [{"label": "Mainly persistent redness and flushing", "value": "erythematotelangiectatic"}, {"label": "Mainly bumps and pus-filled spots", "value": "papulopus<PERSON><PERSON>"}, {"label": "Mainly thickened skin (especially nose)", "value": "phymatous"}, {"label": "Mainly eye symptoms", "value": "ocular"}, {"label": "Equal mix of redness and bumps", "value": "mixed"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Predominant rosacea subtype:", "customConditional": "show = !!data.rosacea_features_present;", "optionsLabelPosition": "right"}, {"key": "heading_triggers_exacerbating_factors", "html": "</br><h4>Triggers &amp; Flare Factors</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "environmental_triggers", "type": "selectboxes", "input": true, "label": "Which environmental factors seem to trigger or worsen your rosacea? (Select all that apply)", "inline": false, "values": [{"label": "Heat or sweating (exercise, hot rooms)", "value": "heat_sweat"}, {"label": "Sun exposure", "value": "sun"}, {"label": "Wind or cold air", "value": "wind_cold"}, {"label": "High humidity", "value": "humidity"}, {"label": "Sudden temperature changes (hot showers/saunas)", "value": "temp_change"}, {"label": "Occlusive masks/helmets touching the face", "value": "occlusion_mask"}, {"label": "Pollution or oily work environment", "value": "pollution"}, {"label": "Chlorine pools or hot tubs", "value": "chlorine"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "adminFlag": true, "tableView": true, "confirm_label": "Phase that summarizes the question:", "optionsLabelPosition": "right"}, {"key": "environmental_triggers_other", "type": "textarea", "input": true, "label": "Please specify other environmental triggers:", "tableView": true, "autoExpand": false, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = !!data.environmental_triggers && data.environmental_triggers.other === true;"}, {"key": "lifestyle_triggers", "type": "selectboxes", "input": true, "label": "Which lifestyle or dietary factors make your rosacea worse? (Select all that apply)", "inline": false, "values": [{"label": "Spicy foods", "value": "spicy_foods"}, {"label": "Hot drinks (coffee/tea/cocoa)", "value": "hot_drinks"}, {"label": "Alcohol (e.g., red wine, spirits)", "value": "alcohol"}, {"label": "Stress or strong emotions", "value": "stress"}, {"label": "Poor sleep", "value": "poor_sleep"}, {"label": "Intense exercise", "value": "intense_exercise"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "adminFlag": true, "tableView": true, "confirm_label": "Phase that summarizes the question:", "optionsLabelPosition": "right"}, {"key": "lifestyle_triggers_other", "type": "textarea", "input": true, "label": "Please specify other lifestyle triggers:", "tableView": true, "autoExpand": false, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = !!data.lifestyle_triggers && data.lifestyle_triggers.other === true;"}, {"key": "product_triggers", "type": "selectboxes", "input": true, "label": "Have any products or medicines on the face triggered flares? (Select all that apply)", "inline": false, "values": [{"label": "Fragranced skincare or alcohol-based toners", "value": "fragrance_alcohol_toner"}, {"label": "Heavy/occlusive creams or make-up", "value": "comedogenic_makeup"}, {"label": "Chemical sunscreens (some people are sensitive)", "value": "chemical_sunscreen"}, {"label": "Topical steroid used on the face", "value": "topical_steroid_face"}, {"label": "Retinoids, benzoyl peroxide, or strong acids (stinging/irritation)", "value": "strong_actives"}, {"label": "Hair oils/pomades touching the skin", "value": "hair_products"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "adminFlag": true, "tableView": true, "confirm_label": "Phase that summarizes the question:", "optionsLabelPosition": "right"}, {"key": "product_triggers_other", "type": "textarea", "input": true, "label": "Please specify other product or medication triggers:", "tableView": true, "autoExpand": false, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = !!data.product_triggers && data.product_triggers.other === true;"}, {"key": "abx_allergy", "type": "selectboxes", "input": true, "label": "Any allergies or major side-effects to these rosacea treatments?", "inline": false, "values": [{"label": "Doxycycline (oral)", "value": "doxy"}, {"label": "Minocycline (oral)", "value": "mino"}, {"label": "Metronidazole (topical gel/cream)", "value": "metronidazole_topical"}, {"label": "Ivermectin (topical)", "value": "ivermectin_topical"}, {"label": "Sulfur or sulfacetamide products (topical)", "value": "sulfur_sulfacetamide"}], "adminFlag": true, "tableView": true, "confirm_label": "Phase that summarizes the question:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_abx_allergy", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a treatment."}, "validate": {"custom": "valid = !!data.none_of_the_above_abx_allergy || _.some(_.values(data.abx_allergy));"}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customClass": "mt-n3", "defaultValue": false}, {"key": "abx_allergy_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have allergies or major side-effects to the following treatments:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Phase that summarizes the question:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.abx_allergy, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "trigger_avoidance_attempted", "type": "radio", "input": true, "label": "Have you tried avoiding any of the triggers you selected?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Partially", "value": "partially"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = (data.environmental_triggers && Object.values(data.environmental_triggers).some(<PERSON><PERSON>an)) || (data.lifestyle_triggers && Object.values(data.lifestyle_triggers).some(Boolean)) || (data.product_triggers && Object.values(data.product_triggers).some(<PERSON><PERSON><PERSON>));", "optionsLabelPosition": "right"}, {"key": "trigger_avoidance_effect", "type": "select", "input": true, "label": "If you tried avoidance, what effect did it have?", "widget": "html5", "data": {"values": [{"label": "No change", "value": "no_change"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "Marked improvement", "value": "marked_improvement"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.trigger_avoidance_attempted === 'yes' || data.trigger_avoidance_attempted === 'partially';"}, {"key": "pregnancy_status", "type": "radio", "input": true, "label": "Are you currently…", "inline": false, "values": [{"label": "Pregnant", "value": "pregnant"}, {"label": "Breast-feeding", "value": "breastfeeding"}, {"label": "Trying / might be soon", "value": "ttc"}, {"label": "None of the above", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.sex === 'female';", "optionsLabelPosition": "right"}, {"key": "heading_hormonal_rosacea", "html": "</br><h4>Hormonal Patterns</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.sex == 'female';"}, {"key": "cycle_flare_pattern", "type": "radio", "input": true, "label": "Do your rosacea flares usually worsen in the week before your period?", "inline": false, "values": [{"label": "Yes - almost every cycle", "value": "yes_regular"}, {"label": "Sometimes / occasional", "value": "sometimes"}, {"label": "No noticeable pattern", "value": "no_pattern"}, {"label": "I don't have periods (e.g., IUD, menopause)", "value": "no_periods"}, {"label": "Unsure / irregular cycles", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.sex == 'female';", "optionsLabelPosition": "right"}, {"key": "cycle_regularity", "type": "select", "input": true, "label": "How regular are your menstrual cycles?", "widget": "html5", "data": {"values": [{"label": "Regular (21-35 days, predictable)", "value": "regular"}, {"label": "Irregular / varying by more than 7 days", "value": "irregular"}, {"label": "No periods for over 3 months (not pregnant)", "value": "amenorrhea"}, {"label": "Using a medication or IUD that stops periods", "value": "suppressed"}, {"label": "Menopausal / post-menopausal", "value": "menopause"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.sex == 'female';"}, {"key": "hormonal_changes_duration", "type": "select", "input": true, "label": "Are these cycle-related changes new or long-standing?", "widget": "html5", "data": {"values": [{"label": "New (within the last 12 months)", "value": "recent"}, {"label": "Long-standing (more than 1 year)", "value": "longstanding"}, {"label": "Unsure", "value": "unsure"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.sex == 'female' && (data.cycle_regularity && data.cycle_regularity !== 'regular');"}, {"key": "pcos_previous_diagnosis", "type": "radio", "input": true, "label": "Have you ever been told you have polycystic ovary syndrome (PCOS)?", "inline": false, "values": [{"label": "Yes - confirmed by a doctor", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.sex == 'female' && (data.cycle_regularity && ['irregular','amenorrhea'].includes(data.cycle_regularity));", "optionsLabelPosition": "right"}, {"key": "rec_box_cycle_change", "html": "<div class=\"alert alert-success\"><p><strong>Based on your recent period changes, an in-person check-up is a good next step.</strong><br>Your family doctor-or a nearby clinic-can arrange hormone blood tests and, if needed, a pelvic ultrasound to look for the cause.</p></div>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.sex === 'female' && (data.cycle_regularity === 'irregular' || data.cycle_regularity === 'amenorrhea') && data.hormonal_changes_duration === 'recent';"}, {"key": "rec_ack_cycle_change", "type": "radio", "input": true, "label": "Do you understand this recommendation?", "inline": false, "values": [{"label": "Yes, I understand", "value": "understand"}, {"label": "No, please clarify", "value": "dont_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.sex === 'female' && (data.cycle_regularity === 'irregular' || data.cycle_regularity === 'amenorrhea') && data.hormonal_changes_duration === 'recent';", "optionsLabelPosition": "right"}, {"key": "rec_box_cycle_change_clarify", "html": "<div class=\"alert alert-info\" style=\"border:1px solid #0dcaf0;background:#cff4fc;color:#055160;border-radius:6px;padding:0.9rem;margin:0.75rem 0;\"><p><strong>Here's a bit more detail:</strong></p><ul><li>Irregular or missed periods can be related to conditions such as PCOS or thyroid changes.</li><li>To find the cause, we usually do:<br>&nbsp;&nbsp;• a brief physical exam<br>&nbsp;&nbsp;• hormone blood tests<br>&nbsp;&nbsp;• a pelvic ultrasound</li><li>Depending on the results, you might be referred to a gynecologist.</li><li>If you still have questions, you can ask our doctor through real-time messaging.</li></ul></div>", "type": "content", "input": false, "label": "Cycle-change clarification", "tableView": false, "clearOnHide": false, "customConditional": "show = data.rec_ack_cycle_change === 'dont_understand';"}, {"key": "androgen_signs", "type": "selectboxes", "input": true, "label": "Have you noticed any of these body changes? (Select all that apply)", "values": [{"label": "More coarse hair on the chin or upper lip", "value": "hirsutism"}, {"label": "Thinning hair on the scalp or a receding hairline", "value": "androgenic_alopecia"}, {"label": "Unexplained weight gain or hard to lose weight", "value": "weight_gain"}, {"label": "Dark, velvety skin patches on the neck or underarms", "value": "acanthosis"}], "adminFlag": true, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.sex == 'female';", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_androgen_signs", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Select at least one sign or choose 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_androgen_signs || _.some(_.values(data.androgen_signs));"}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.sex == 'female';"}, {"key": "current_hormonal_contraception", "type": "select", "input": true, "label": "Are you currently using any hormonal contraception?", "widget": "html5", "data": {"values": [{"label": "Combined pill (estrogen + progestin)", "value": "combined_pill"}, {"label": "Progestin-only pill", "value": "pop"}, {"label": "Hormonal IUD (Mirena®, Kyleena®, etc.)", "value": "iud_hormonal"}, {"label": "Depot injection (Depo-Provera®)", "value": "depo"}, {"label": "Hormonal implant (Nexplanon®)", "value": "implant"}, {"label": "No hormonal contraception", "value": "none"}, {"label": "Prefer not to say", "value": "na"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.sex == 'female';"}, {"key": "heading_medication_triggers_rosacea", "html": "</br><h4>Could a Medicine Be Fueling Your Rosacea?</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "med_trigger_info_rosacea", "html": "<div class=\"alert alert-info\" style=\"border:1px solid #0dcaf0;background:#cff4fc;color:#055160;border-radius:6px;padding:0.9rem;margin:0.75rem 0;\"><strong>Heads-up:</strong> Some medicines can make rosacea worse by increasing flushing or irritating the skin. Examples include <em>topical steroids used on the face</em> and some <em>vasodilators</em> (e.g., high-dose niacin, nitrates, certain blood-pressure medicines).<br>Please tell us about any medicine you <u>started or changed <em>before</em> your rosacea first appeared or flared up</u>, even if you didn't notice a link at the time.</div>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "med_trigger_any", "type": "radio", "input": true, "label": "Did you start or switch <strong>any</strong> medicine before your current rosacea flare began or got noticeably worse?", "inline": false, "values": [{"label": "Yes - at least one", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Unsure / can't recall", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "optionsLabelPosition": "right"}, {"key": "med_trigger_categories_common", "type": "selectboxes", "input": true, "label": "Which of these types of medicines did you start or change? (Select all that apply)", "inline": false, "values": [{"label": "Steroid cream or ointment used on your face (like hydrocortisone, cortisone cream)", "value": "topical_steroid_face"}, {"label": "Steroid pills (like prednisone, prednisolone)", "value": "oral_steroid"}, {"label": "Blood pressure medicines", "value": "bp_meds"}, {"label": "Heart medicines or chest pain medicines", "value": "heart_meds"}, {"label": "Erectile dysfunction medicines (like Viagra, Cialis)", "value": "ed_meds"}, {"label": "High-dose vitamin B3 (niacin) supplements", "value": "niacin"}, {"label": "Other prescription or over-the-counter medicine", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "adminFlag": true, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = data.med_trigger_any === 'yes' || data.med_trigger_any === 'unsure';", "optionsLabelPosition": "right"}, {"key": "med_trigger_other_text", "type": "textarea", "input": true, "label": "Please tell us the name of the other medicine:", "tableView": true, "autoExpand": false, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = !!data.med_trigger_categories_common && data.med_trigger_categories_common.other === true;"}, {"key": "med_trigger_timing", "type": "select", "input": true, "label": "About how long <u>before</u> the rosacea worsened did you start the medicine(s) you selected?", "widget": "html5", "data": {"values": [{"label": "Less than 1 month", "value": "lt1mo"}, {"label": "1-3 months", "value": "1to3mo"}, {"label": "3-6 months", "value": "3to6mo"}, {"label": "More than 6 months", "value": "gt6mo"}, {"label": "Unsure", "value": "unsure"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = (_.some(_.omit(data.med_trigger_categories_common,['none'])));"}, {"key": "med_trigger_current_use", "type": "radio", "input": true, "label": "Are you still taking any of the medicine(s) you selected?", "inline": false, "values": [{"label": "Yes - still taking", "value": "still_on"}, {"label": "No - stopped", "value": "stopped"}, {"label": "Some yes / some no", "value": "mixed"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = (_.some(_.omit(data.med_trigger_categories_common,['none'])));", "optionsLabelPosition": "right"}, {"key": "steroid_face_warning_box", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "html": "<div class=\"alert alert-warning\" style=\"border:1px solid #ffc107;background:#fff3cd;color:#664d03;border-radius:6px;padding:0.9rem;margin:0.75rem 0;\"><strong>Important:</strong> Steroid creams used on the face can mimic or worsen rosacea. Do not stop a strong steroid suddenly without medical advice - a <em>taper</em> is sometimes needed. We can help you plan a safer switch to rosacea-friendly treatments.</div>", "customConditional": "show = !!data.med_trigger_categories_common && (data.med_trigger_categories_common.topical_steroid_face || data.med_trigger_categories_common.oral_steroid);"}, {"key": "steroid_warning_ack", "type": "radio", "input": true, "label": "Do you understand this advice about steroid creams on the face?", "inline": false, "values": [{"label": "Yes, I understand", "value": "understand"}, {"label": "No, please clarify", "value": "dont_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phase that summarizes the question:", "customConditional": "show = !!data.med_trigger_categories_common && (data.med_trigger_categories_common.topical_steroid_face || data.med_trigger_categories_common.oral_steroid);", "optionsLabelPosition": "right"}, {"key": "photo_upload_header", "html": "</br><h2>Photo Upload</h2><p>Please upload a clear photo of <strong>each affected area</strong>. Our doctors <em>require</em> these images in order to confirm rosacea and prescribe treatment. Good lighting is helpful.</p>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "uploadUrl", "url": "/app/q/{qpk}/formio-files/{pk}/", "type": "file", "image": true, "input": true, "label": "Upload photos", "webcam": true, "capture": "user", "storage": "url", "multiple": true, "fileTypes": [{"label": "", "value": ""}], "tableView": true, "validateWhenHidden": false, "confirm_label": "Photos uploaded:"}, {"key": "photo_confirm_face", "type": "checkbox", "input": true, "label": "I've uploaded at least one clear photo of my face / neck.", "validate": {"required": true}, "tableView": true, "confirm_label": "Face/neck photo uploaded:", "customConditional": "show = _.some(_.values(data.rosacea_face_areas || {}));"}, {"key": "photo_confirm_other", "type": "checkbox", "input": true, "label": "I've uploaded at least one clear photo of another area affected by rosacea (if present).", "validate": {"required": true}, "tableView": true, "confirm_label": "Other area photo uploaded:", "customConditional": "show = !!data.rosacea_distribution_other;"}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = [];", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-acne':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-req','get-rx','appointment-intake','edit-intake']"}]}