{"components": [{"key": "diagnosis_of_gout", "type": "radio", "input": true, "label": "Have you been diagnosed with gout by a healthcare provider?", "confirm_label": "Gout diagnosis", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "gout_red_flag_symptoms", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following symptoms?", "confirm_label": "Red flag symptoms", "values": [{"label": "Fever", "value": "fever"}, {"label": "Nausea or vomiting", "value": "nausea_vomiting"}, {"label": "Feel unwell", "value": "feel_unwell"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.diagnosis_of_gout === 'yes';", "optionsLabelPosition": "right"}, {"key": "no_gout_red_flag_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_gout_red_flag_symptoms || !!_.some(_.values(data.gout_red_flag_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.diagnosis_of_gout === 'yes';"}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indication", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "defaultValue": true, "calculateValue": "value = (data.diagnosis_of_gout === 'yes' && !_.some(_.values(data.gout_red_flag_symptoms))) ? false : true;", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = []; if (data.diagnosis_of_gout !== 'yes') { value.push('no_gout_diagnosis'); } if (_.some(_.values(data.gout_red_flag_symptoms))) { value.push('red_flag_symptoms'); }", "refreshOnChange": true}, {"key": "approved_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-green'>You're eligible to discuss gout treatment.</h3><p>TeleTest physicians can prescribe medications for acute gout attacks, including:</p><ul><li><strong>Colchicine</strong> – helps treat flares and prevent future attacks</li><li><strong>Indomethacin</strong> – reduces pain and swelling during attacks</li></ul><p>For patients already on long-term therapy, we can renew or adjust:</p><ul><li><strong>Allopurinol</strong></li><li><strong>Febuxostat</strong></li></ul><p>We can also arrange uric acid testing to ensure your dose is appropriate.</p><p><strong>Not prescribed through TeleTest:</strong></p><ul><li>Prednisone or other steroid medications</li><li>Injectable gout treatments</li></ul><p><strong>Pharmacy options:</strong> Prescriptions can be sent to your local pharmacy or delivered to your door through our partner network.</p>"}, {"key": "rejected_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-red'>You're ineligible for gout treatment at this time. Please seek in-person care with a physician to discuss your concerns.</h3>", "refreshOnChange": true}]}